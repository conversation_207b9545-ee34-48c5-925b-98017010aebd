// src/scenes/shanhaijingWorld.js
// 山海经世界场景 - 集成地形生成和环境系统的完整场景

import {
    Scene,
    Engine,
    Vector3,
    Color3,
    ArcRotateCamera,
    HavokPlugin,
    PhysicsBody,
    PhysicsMotionType,
    PhysicsShapeBox,
    Quaternion,
    MeshBuilder,
    StandardMaterial
} from "@babylonjs/core";
import HavokPhysics from "@babylonjs/havok";
import "@babylonjs/core/Physics/physicsEngineComponent";

import TerrainGenerator from "../world/terrainGenerator.js";
import EnvironmentManager from "../world/environmentManager.js";
import Player from "../entities/player.js";
import { ECSPlayerFactory } from "../entities/ecsPlayer.js";
import InputManager from "../input/inputManager.js";
import { TerrainConfig } from "../config/terrain.js";
import { EnvironmentConfig } from "../config/environment.js";

/**
 * 山海经世界场景类
 * 整合地形生成、环境系统、玩家控制等功能的完整游戏场景
 */
export class ShanhaijingWorldScene {
    constructor() {
        this.scene = null;
        this.engine = null;
        this.camera = null;

        // 核心系统
        this.terrainGenerator = null;
        this.environmentManager = null;
        this.inputManager = null;

        // 玩家系统
        this.localPlayer = null;
        this.remotePlayers = new Map();
        this.ecsPlayerFactory = null;

        // 性能监控
        this.lastFrameTime = 0;
        this.frameCount = 0;
        this.fps = 0;

        console.log("山海经世界场景已创建");
    }

    /**
     * 创建场景
     * @param {Engine} engine - Babylon.js引擎实例
     * @param {HTMLCanvasElement} canvas - 画布元素
     * @returns {Scene} 创建的场景
     */
    async createScene(engine, canvas) {
        console.log("开始创建山海经世界场景...");

        this.engine = engine;

        // 创建基础场景
        this.scene = new Scene(engine);
        this.scene.clearColor = new Vector3(0.5, 0.7, 1.0); // 天空蓝色背景

        // 将ECS管理器引用添加到场景
        if (window.ecsIntegration?.ecs?.manager) {
            this.scene.ecsManager = window.ecsIntegration.ecs.manager;
            console.log("ECS管理器已添加到场景");
        }

        // 初始化物理引擎
        await this.initializePhysics();

        // 创建相机
        this.createCamera(canvas);

        // 初始化地形系统
        await this.initializeTerrain();

        // 初始化环境系统
        await this.initializeEnvironment();

        // 初始化玩家系统
        await this.initializePlayer();

        // 初始化输入系统
        this.initializeInput();

        // 设置渲染循环
        this.setupRenderLoop();

        // 设置调试信息
        this.setupDebugInfo();

        console.log("山海经世界场景创建完成");
        return this.scene;
    }

    /**
     * 初始化物理引擎
     */
    async initializePhysics() {
        console.log("初始化Havok物理引擎...");

        try {
            const havokInstance = await HavokPhysics();
            const havokPlugin = new HavokPlugin(true, havokInstance);
            this.scene.enablePhysics(new Vector3(0, -9.81, 0), havokPlugin);
            console.log("Havok物理引擎初始化完成");
        } catch (error) {
            console.error("物理引擎初始化失败:", error);
            throw error;
        }
    }

    /**
     * 创建相机
     * @param {HTMLCanvasElement} canvas - 画布元素
     */
    createCamera(canvas) {
        console.log("创建相机...");

        // 创建弧度旋转相机（第三人称视角）
        this.camera = new ArcRotateCamera(
            "mainCamera",
            Math.PI / 4,        // alpha - 水平旋转角度
            Math.PI / 3,        // beta - 垂直旋转角度
            30,                 // radius - 距离目标的距离
            Vector3.Zero(),     // target - 相机目标点
            this.scene
        );

        // 设置相机控制
        this.camera.attachControl(canvas, true);

        // 设置相机限制
        this.camera.lowerRadiusLimit = 5;      // 最小距离
        this.camera.upperRadiusLimit = 200;    // 最大距离
        this.camera.lowerBetaLimit = 0.1;      // 最小垂直角度
        this.camera.upperBetaLimit = Math.PI / 2.2; // 最大垂直角度

        // 设置相机移动速度
        this.camera.wheelPrecision = 50;       // 滚轮缩放精度
        this.camera.panningSensibility = 1000; // 平移敏感度

        console.log("相机创建完成");
    }

    /**
     * 初始化地形系统
     */
    async initializeTerrain() {
        console.log("初始化地形系统...");

        try {
            // 使用山海经主题的中山经配置
            const terrainConfig = TerrainConfig.shanhaijing.centralMountains;

            // 创建地形生成器
            this.terrainGenerator = new TerrainGenerator(this.scene, terrainConfig);

            // 生成地形
            const terrain = this.terrainGenerator.generateTerrain('mountains', 55555);

            if (terrain) {
                console.log("地形生成成功");

                // 设置地形接收阴影
                terrain.receiveShadows = true;

                // 优化地形渲染
                terrain.freezeWorldMatrix();
                terrain.doNotSyncBoundingInfo = true;
            } else {
                console.error("地形生成失败");
            }

        } catch (error) {
            console.error("地形系统初始化失败:", error);

            // 创建备用平面地形
            this.createFallbackTerrain();
        }
    }

    /**
     * 创建备用地形（当主地形生成失败时使用）
     */
    createFallbackTerrain() {
        console.log("创建备用平面地形...");

        const ground = MeshBuilder.CreateGround("fallbackGround", { width: 200, height: 200 }, this.scene);

        // 创建简单材质
        const groundMaterial = new StandardMaterial("fallbackGroundMaterial", this.scene);
        groundMaterial.diffuseColor = new Color3(0.3, 0.5, 0.2); // 绿色
        ground.material = groundMaterial;

        // 添加物理属性
        const groundPhysicsBody = new PhysicsBody(ground, PhysicsMotionType.STATIC, false, this.scene);
        const groundShape = new PhysicsShapeBox(
            new Vector3(0, 0, 0),
            new Quaternion(0, 0, 0, 1),
            new Vector3(200, 0.1, 200),
            this.scene
        );
        groundShape.material = { friction: 0.8, restitution: 0.1 };
        groundPhysicsBody.shape = groundShape;
        groundPhysicsBody.setMassProperties({ mass: 0 });

        console.log("备用地形创建完成");
    }

    /**
     * 初始化环境系统
     */
    async initializeEnvironment() {
        console.log("初始化环境系统...");

        try {
            // 使用山海经主题的环境配置
            const envConfig = {
                ...EnvironmentConfig,
                dayNightCycle: {
                    ...EnvironmentConfig.dayNightCycle,
                    duration: 600000 // 10分钟一个昼夜循环（用于演示）
                }
            };

            // 创建环境管理器
            this.environmentManager = new EnvironmentManager(this.scene, envConfig);

            // 设置初始时间为早晨
            this.environmentManager.setTimeOfDay(0.3); // 早晨7:12

            console.log("环境系统初始化完成");

        } catch (error) {
            console.error("环境系统初始化失败:", error);
        }
    }

    /**
     * 初始化玩家系统
     */
    async initializePlayer() {
        console.log("初始化玩家系统...");

        try {
            // 获取ECS管理器
            const ecsManager = this.scene.ecsManager || window.ecsIntegration?.ecs?.manager;

            if (ecsManager) {
                // 使用ECS系统创建玩家
                this.ecsPlayerFactory = new ECSPlayerFactory(ecsManager);

                // 生成本地玩家ID
                const localPlayerId = "localPlayer_" + Math.random().toString(36).substring(2, 9);

                // 创建本地玩家，初始位置在地形中心稍高的位置
                const spawnPosition = new Vector3(0, 10, 0);
                this.localPlayer = this.ecsPlayerFactory.createPlayer(localPlayerId, spawnPosition, true);

                // 获取玩家的变换组件来设置相机跟随
                const transform = this.localPlayer.getComponent('TransformComponent');
                if (transform) {
                    this.camera.setTarget(transform.position);
                }

                console.log(`ECS本地玩家创建完成: ${localPlayerId}`);
            } else {
                // 回退到传统玩家系统
                console.warn("ECS系统不可用，使用传统玩家系统");

                const localPlayerId = "localPlayer_" + Math.random().toString(36).substring(2, 9);
                const spawnPosition = new Vector3(0, 10, 0);
                this.localPlayer = new Player(localPlayerId, this.scene, spawnPosition, true);

                // 设置相机跟随玩家
                this.camera.setTarget(this.localPlayer.mesh.position);

                console.log(`传统本地玩家创建完成: ${localPlayerId}`);
            }

        } catch (error) {
            console.error("玩家系统初始化失败:", error);
        }
    }

    /**
     * 初始化输入系统
     */
    initializeInput() {
        console.log("初始化输入系统...");

        try {
            this.inputManager = new InputManager(this.scene);
            this.inputManager.init();

            console.log("输入系统初始化完成");

        } catch (error) {
            console.error("输入系统初始化失败:", error);
        }
    }

    /**
     * 设置渲染循环
     */
    setupRenderLoop() {
        console.log("设置渲染循环...");

        // 注册渲染前回调
        this.scene.onBeforeRenderObservable.add(() => {
            const deltaTime = this.engine.getDeltaTime() / 1000.0;

            // 更新玩家
            if (this.localPlayer) {
                if (this.ecsPlayerFactory) {
                    // ECS玩家更新（ECS系统会自动处理输入和更新）
                    const transform = this.localPlayer.getComponent('TransformComponent');
                    if (transform) {
                        // 更新相机跟随
                        this.camera.setTarget(transform.position);
                    }
                } else if (this.inputManager) {
                    // 传统玩家更新
                    const movementInput = this.inputManager.update();
                    this.localPlayer.applyMovementInput(movementInput);

                    // 处理跳跃
                    if (this.inputManager.getJumpInput()) {
                        if (this.localPlayer.physicsBody.getLinearVelocity().y <= 0.1) {
                            this.localPlayer.physicsBody.applyImpulse(
                                new Vector3(0, 8, 0),
                                this.localPlayer.mesh.position
                            );
                            this.inputManager.resetJumpInput();
                        }
                    }

                    // 更新玩家状态
                    this.localPlayer.update(deltaTime);

                    // 更新相机跟随
                    this.camera.setTarget(this.localPlayer.mesh.position);
                }
            }

            // 更新环境系统
            if (this.environmentManager) {
                this.environmentManager.update(deltaTime);
            }

            // 更新远程玩家
            this.remotePlayers.forEach(player => {
                player.update(deltaTime);
            });

            // 更新性能统计
            this.updatePerformanceStats();
        });

        console.log("渲染循环设置完成");
    }

    /**
     * 设置调试信息
     */
    setupDebugInfo() {
        // 将场景和关键对象暴露到全局，便于调试
        if (typeof window !== 'undefined') {
            window.shanhaijingScene = this.scene;
            window.terrainGenerator = this.terrainGenerator;
            window.environmentManager = this.environmentManager;
            window.localPlayer = this.localPlayer;

            console.log("调试对象已暴露到全局作用域");
        }
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats() {
        this.frameCount++;
        const currentTime = performance.now();

        if (currentTime - this.lastFrameTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFrameTime = currentTime;

            // 可以在这里添加性能监控逻辑
            if (this.fps < 30) {
                console.warn(`性能警告: FPS降至 ${this.fps}`);
            }
        }
    }

    /**
     * 添加远程玩家
     * @param {string} playerId - 玩家ID
     * @param {Vector3} position - 初始位置
     * @param {Quaternion} rotation - 初始旋转
     */
    addRemotePlayer(playerId, position, rotation) {
        if (this.remotePlayers.has(playerId)) {
            console.warn(`远程玩家 ${playerId} 已存在`);
            return;
        }

        const remotePlayer = new Player(playerId, this.scene, position, false);
        if (rotation) {
            remotePlayer.mesh.rotationQuaternion = rotation;
        }

        this.remotePlayers.set(playerId, remotePlayer);
        console.log(`远程玩家 ${playerId} 已添加`);
    }

    /**
     * 移除远程玩家
     * @param {string} playerId - 玩家ID
     */
    removeRemotePlayer(playerId) {
        const player = this.remotePlayers.get(playerId);
        if (player) {
            player.dispose();
            this.remotePlayers.delete(playerId);
            console.log(`远程玩家 ${playerId} 已移除`);
        }
    }

    /**
     * 获取场景信息
     * @returns {Object} 场景信息
     */
    getSceneInfo() {
        return {
            fps: this.fps,
            playerCount: this.remotePlayers.size + (this.localPlayer ? 1 : 0),
            timeInfo: this.environmentManager ? this.environmentManager.getTimeInfo() : null,
            terrainLoaded: !!this.terrainGenerator,
            environmentLoaded: !!this.environmentManager
        };
    }

    /**
     * 切换天气
     * @param {string} weatherType - 天气类型
     */
    setWeather(weatherType) {
        if (this.environmentManager) {
            this.environmentManager.setWeather(weatherType);
        }
    }

    /**
     * 设置时间
     * @param {number} timeOfDay - 时间（0-1）
     */
    setTimeOfDay(timeOfDay) {
        if (this.environmentManager) {
            this.environmentManager.setTimeOfDay(timeOfDay);
        }
    }

    /**
     * 释放资源
     */
    dispose() {
        console.log("释放山海经世界场景资源...");

        // 释放玩家
        if (this.ecsPlayerFactory) {
            this.ecsPlayerFactory.cleanup();
            this.ecsPlayerFactory = null;
        } else if (this.localPlayer) {
            this.localPlayer.dispose();
        }

        this.remotePlayers.forEach(player => player.dispose());
        this.remotePlayers.clear();

        // 释放系统
        if (this.terrainGenerator) {
            this.terrainGenerator.dispose();
        }

        if (this.environmentManager) {
            this.environmentManager.dispose();
        }

        // 释放场景
        if (this.scene) {
            this.scene.dispose();
        }

        console.log("山海经世界场景资源释放完成");
    }
}

export default ShanhaijingWorldScene;
