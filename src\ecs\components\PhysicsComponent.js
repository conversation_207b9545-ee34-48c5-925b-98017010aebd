// src/ecs/components/PhysicsComponent.js
// 物理组件 - 管理实体的物理属性和Havok物理引擎集成

import { 
    Vector3, 
    PhysicsImpostor,
    PhysicsAggregate
} from '@babylonjs/core';
import Component from '../Component.js';

/**
 * 物理组件类
 * 管理实体的物理属性，包括刚体、碰撞体、物理材质等
 * 依赖TransformComponent和RenderComponent
 */
export class PhysicsComponent extends Component {
    /**
     * 构造函数
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     */
    constructor(entity = null, options = {}) {
        super(entity, options);
        
        // 设置依赖组件
        this.dependencies = ['TransformComponent'];
        
        // 物理引擎对象
        this.physicsAggregate = null;
        this.physicsImpostor = null; // 兼容旧版本API
        
        // 物理配置
        this.physicsConfig = {
            type: options.type || 'box',                    // 物理形状类型
            mass: options.mass !== undefined ? options.mass : 1.0,  // 质量
            restitution: options.restitution || 0.3,       // 弹性系数
            friction: options.friction || 0.5,             // 摩擦系数
            linearDamping: options.linearDamping || 0.1,   // 线性阻尼
            angularDamping: options.angularDamping || 0.1, // 角阻尼
            isStatic: options.isStatic || false,           // 是否为静态物体
            isTrigger: options.isTrigger || false,         // 是否为触发器
            enableGravity: options.enableGravity !== false, // 是否受重力影响
            freezeRotation: options.freezeRotation || false, // 是否冻结旋转
            collisionGroup: options.collisionGroup || 1,   // 碰撞组
            collisionMask: options.collisionMask || -1     // 碰撞遮罩
        };
        
        // 物理形状选项
        this.shapeOptions = {
            size: options.size || new Vector3(1, 1, 1),
            radius: options.radius || 0.5,
            height: options.height || 2.0,
            ...options.shapeOptions
        };
        
        // 运动状态
        this.velocity = Vector3.Zero();
        this.angularVelocity = Vector3.Zero();
        this.force = Vector3.Zero();
        this.torque = Vector3.Zero();
        
        // 碰撞检测
        this.collisionCallbacks = new Map();
        this.triggerCallbacks = new Map();
        
        // 物理状态
        this.isPhysicsEnabled = true;
        this.isInitialized = false;
        this.needsSync = false;
        
        // 约束和限制
        this.constraints = {
            positionLock: { x: false, y: false, z: false },
            rotationLock: { x: false, y: false, z: false },
            maxVelocity: options.maxVelocity || null,
            maxAngularVelocity: options.maxAngularVelocity || null
        };
        
        // 性能统计
        this.physicsStats = {
            collisionCount: 0,
            lastCollisionTime: 0,
            averageVelocity: 0,
            totalForceApplied: 0
        };
        
        console.log(`物理组件已创建: ${this.entity?.id}`);
    }
    
    /**
     * 组件初始化
     */
    onInitialize() {
        // 获取依赖组件
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) {
            console.error(`物理组件缺少TransformComponent依赖: ${this.entity?.id}`);
            return;
        }
        
        // 创建物理体
        this.createPhysicsBody();
        
        // 监听变换变化
        transformComponent.on('transformChanged', () => {
            this.syncFromTransform();
        });
        
        this.isInitialized = true;
        
        // 触发物理组件就绪事件
        this.emit('physicsReady', { component: this });
    }
    
    /**
     * 创建物理体
     * @private
     */
    createPhysicsBody() {
        if (!this.entity?.scene) {
            console.error('无法创建物理体：缺少场景引用');
            return;
        }
        
        const scene = this.entity.scene;
        const transformComponent = this.getDependency('TransformComponent');
        
        // 检查场景是否启用了物理引擎
        if (!scene.getPhysicsEngine()) {
            console.error('场景未启用物理引擎');
            return;
        }
        
        try {
            // 获取或创建物理网格
            let physicsMesh = this.getPhysicsMesh();
            
            if (!physicsMesh) {
                console.error('无法获取物理网格');
                return;
            }
            
            // 设置初始变换
            physicsMesh.position.copyFrom(transformComponent.position);
            physicsMesh.rotationQuaternion = transformComponent.rotation.clone();
            physicsMesh.scaling.copyFrom(transformComponent.scale);
            
            // 创建物理聚合体（Havok新API）
            const physicsShape = this.getPhysicsShape();
            const physicsOptions = {
                mass: this.physicsConfig.isStatic ? 0 : this.physicsConfig.mass,
                restitution: this.physicsConfig.restitution,
                friction: this.physicsConfig.friction
            };
            
            this.physicsAggregate = new PhysicsAggregate(
                physicsMesh,
                physicsShape,
                physicsOptions,
                scene
            );
            
            // 设置物理属性
            this.applyPhysicsProperties();
            
            // 设置碰撞回调
            this.setupCollisionCallbacks();
            
            console.log(`物理体已创建: ${this.physicsConfig.type} for ${this.entity?.id}`);
            
        } catch (error) {
            console.error('创建物理体失败:', error);
        }
    }
    
    /**
     * 获取物理网格
     * @private
     * @returns {BABYLON.Mesh} 物理网格
     */
    getPhysicsMesh() {
        // 尝试从RenderComponent获取网格
        const renderComponent = this.entity.getComponent('RenderComponent');
        if (renderComponent && renderComponent.mesh) {
            return renderComponent.mesh;
        }
        
        // 如果没有渲染组件，创建一个不可见的物理网格
        return this.createInvisiblePhysicsMesh();
    }
    
    /**
     * 创建不可见的物理网格
     * @private
     * @returns {BABYLON.Mesh} 不可见的物理网格
     */
    createInvisiblePhysicsMesh() {
        const scene = this.entity.scene;
        const { MeshBuilder } = require('@babylonjs/core');
        
        let mesh;
        
        switch (this.physicsConfig.type) {
            case 'box':
                mesh = MeshBuilder.CreateBox(
                    `${this.entity.id}_physics_box`,
                    { 
                        width: this.shapeOptions.size.x,
                        height: this.shapeOptions.size.y,
                        depth: this.shapeOptions.size.z
                    },
                    scene
                );
                break;
                
            case 'sphere':
                mesh = MeshBuilder.CreateSphere(
                    `${this.entity.id}_physics_sphere`,
                    { diameter: this.shapeOptions.radius * 2 },
                    scene
                );
                break;
                
            case 'capsule':
                mesh = MeshBuilder.CreateCapsule(
                    `${this.entity.id}_physics_capsule`,
                    { 
                        radius: this.shapeOptions.radius,
                        height: this.shapeOptions.height
                    },
                    scene
                );
                break;
                
            default:
                mesh = MeshBuilder.CreateBox(
                    `${this.entity.id}_physics_box`,
                    { size: 1 },
                    scene
                );
        }
        
        // 设置为不可见
        mesh.isVisible = false;
        
        return mesh;
    }
    
    /**
     * 获取物理形状类型
     * @private
     * @returns {number} 物理形状类型
     */
    getPhysicsShape() {
        const { PhysicsShapeType } = require('@babylonjs/core');
        
        switch (this.physicsConfig.type) {
            case 'box':
                return PhysicsShapeType.BOX;
            case 'sphere':
                return PhysicsShapeType.SPHERE;
            case 'capsule':
                return PhysicsShapeType.CAPSULE;
            case 'cylinder':
                return PhysicsShapeType.CYLINDER;
            case 'mesh':
                return PhysicsShapeType.MESH;
            case 'convexHull':
                return PhysicsShapeType.CONVEX_HULL;
            default:
                return PhysicsShapeType.BOX;
        }
    }
    
    /**
     * 应用物理属性
     * @private
     */
    applyPhysicsProperties() {
        if (!this.physicsAggregate) return;
        
        const body = this.physicsAggregate.body;
        
        // 设置阻尼
        body.setLinearDamping(this.physicsConfig.linearDamping);
        body.setAngularDamping(this.physicsConfig.angularDamping);
        
        // 设置重力
        if (!this.physicsConfig.enableGravity) {
            body.setGravityFactor(0);
        }
        
        // 设置运动类型
        if (this.physicsConfig.isStatic) {
            body.setMotionType(1); // Static
        } else if (this.physicsConfig.isTrigger) {
            body.setMotionType(3); // Trigger
        } else {
            body.setMotionType(2); // Dynamic
        }
        
        // 设置碰撞组和遮罩
        body.setCollisionGroup(this.physicsConfig.collisionGroup);
        body.setCollisionMask(this.physicsConfig.collisionMask);
        
        // 应用约束
        this.applyConstraints();
    }
    
    /**
     * 应用约束
     * @private
     */
    applyConstraints() {
        if (!this.physicsAggregate) return;
        
        const body = this.physicsAggregate.body;
        
        // 位置锁定
        const { positionLock, rotationLock } = this.constraints;
        if (positionLock.x || positionLock.y || positionLock.z) {
            // 这里需要根据具体的物理引擎API来实现位置锁定
            // Havok的具体实现可能不同
        }
        
        // 旋转锁定
        if (this.physicsConfig.freezeRotation || 
            rotationLock.x || rotationLock.y || rotationLock.z) {
            // 冻结旋转
            body.setAngularVelocity(Vector3.Zero());
        }
    }
    
    /**
     * 设置碰撞回调
     * @private
     */
    setupCollisionCallbacks() {
        if (!this.physicsAggregate) return;
        
        const body = this.physicsAggregate.body;
        
        // 碰撞开始回调
        body.setCollisionCallbackEnabled(true);
        body.getCollisionObservable().add((collisionEvent) => {
            this.handleCollision(collisionEvent);
        });
    }
    
    /**
     * 处理碰撞事件
     * @private
     * @param {Object} collisionEvent - 碰撞事件
     */
    handleCollision(collisionEvent) {
        this.physicsStats.collisionCount++;
        this.physicsStats.lastCollisionTime = performance.now();
        
        // 触发碰撞事件
        this.emit('collision', {
            component: this,
            collisionEvent,
            otherBody: collisionEvent.collidedAgainst
        });
        
        // 执行注册的碰撞回调
        this.collisionCallbacks.forEach((callback, key) => {
            try {
                callback(collisionEvent);
            } catch (error) {
                console.error(`碰撞回调执行错误 ${key}:`, error);
            }
        });
    }
    
    /**
     * 应用力
     * @param {Vector3} force - 力向量
     * @param {Vector3} [point] - 作用点（世界坐标）
     */
    applyForce(force, point = null) {
        if (!this.physicsAggregate || this.physicsConfig.isStatic) return;
        
        const body = this.physicsAggregate.body;
        
        if (point) {
            body.applyForce(force, point);
        } else {
            body.applyForce(force, body.getObjectCenterWorld());
        }
        
        this.physicsStats.totalForceApplied += force.length();
        
        this.emit('forceApplied', { component: this, force, point });
    }
    
    /**
     * 应用冲量
     * @param {Vector3} impulse - 冲量向量
     * @param {Vector3} [point] - 作用点（世界坐标）
     */
    applyImpulse(impulse, point = null) {
        if (!this.physicsAggregate || this.physicsConfig.isStatic) return;
        
        const body = this.physicsAggregate.body;
        
        if (point) {
            body.applyImpulse(impulse, point);
        } else {
            body.applyImpulse(impulse, body.getObjectCenterWorld());
        }
        
        this.emit('impulseApplied', { component: this, impulse, point });
    }
    
    /**
     * 设置线性速度
     * @param {Vector3} velocity - 速度向量
     */
    setLinearVelocity(velocity) {
        if (!this.physicsAggregate || this.physicsConfig.isStatic) return;
        
        // 应用最大速度限制
        let finalVelocity = velocity.clone();
        if (this.constraints.maxVelocity && finalVelocity.length() > this.constraints.maxVelocity) {
            finalVelocity = finalVelocity.normalize().scale(this.constraints.maxVelocity);
        }
        
        this.physicsAggregate.body.setLinearVelocity(finalVelocity);
        this.velocity.copyFrom(finalVelocity);
        
        this.emit('velocityChanged', { component: this, velocity: finalVelocity });
    }
    
    /**
     * 获取线性速度
     * @returns {Vector3} 当前线性速度
     */
    getLinearVelocity() {
        if (!this.physicsAggregate) return Vector3.Zero();
        
        return this.physicsAggregate.body.getLinearVelocity();
    }
    
    /**
     * 设置角速度
     * @param {Vector3} angularVelocity - 角速度向量
     */
    setAngularVelocity(angularVelocity) {
        if (!this.physicsAggregate || this.physicsConfig.isStatic || this.physicsConfig.freezeRotation) return;
        
        // 应用最大角速度限制
        let finalAngularVelocity = angularVelocity.clone();
        if (this.constraints.maxAngularVelocity && finalAngularVelocity.length() > this.constraints.maxAngularVelocity) {
            finalAngularVelocity = finalAngularVelocity.normalize().scale(this.constraints.maxAngularVelocity);
        }
        
        this.physicsAggregate.body.setAngularVelocity(finalAngularVelocity);
        this.angularVelocity.copyFrom(finalAngularVelocity);
    }
    
    /**
     * 获取角速度
     * @returns {Vector3} 当前角速度
     */
    getAngularVelocity() {
        if (!this.physicsAggregate) return Vector3.Zero();
        
        return this.physicsAggregate.body.getAngularVelocity();
    }
    
    /**
     * 从变换组件同步到物理体
     * @private
     */
    syncFromTransform() {
        if (!this.physicsAggregate || this.needsSync) return;
        
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) return;
        
        const body = this.physicsAggregate.body;
        
        // 同步位置和旋转
        body.setTargetTransform(
            transformComponent.position,
            transformComponent.rotation
        );
    }
    
    /**
     * 从物理体同步到变换组件
     * @private
     */
    syncToTransform() {
        if (!this.physicsAggregate || this.physicsConfig.isStatic) return;
        
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) return;
        
        const body = this.physicsAggregate.body;
        
        // 获取物理体的变换
        const position = body.getObjectCenterWorld();
        const rotation = body.getRotation();
        
        // 更新变换组件
        transformComponent.setPosition(position);
        transformComponent.setRotation(rotation);
    }
    
    /**
     * 添加碰撞回调
     * @param {string} key - 回调键
     * @param {Function} callback - 回调函数
     */
    addCollisionCallback(key, callback) {
        this.collisionCallbacks.set(key, callback);
    }
    
    /**
     * 移除碰撞回调
     * @param {string} key - 回调键
     */
    removeCollisionCallback(key) {
        this.collisionCallbacks.delete(key);
    }
    
    /**
     * 启用/禁用物理
     * @param {boolean} enabled - 是否启用
     */
    setPhysicsEnabled(enabled) {
        this.isPhysicsEnabled = enabled;
        
        if (this.physicsAggregate) {
            this.physicsAggregate.body.setMotionType(enabled ? 2 : 0); // Dynamic : Inactive
        }
        
        this.emit('physicsEnabledChanged', { component: this, enabled });
    }
    
    /**
     * 组件更新
     * @param {number} deltaTime - 时间间隔
     */
    onUpdate(deltaTime) {
        if (!this.isInitialized || !this.isPhysicsEnabled) return;
        
        // 同步物理体状态到变换组件
        if (!this.physicsConfig.isStatic) {
            this.syncToTransform();
        }
        
        // 更新速度统计
        const currentVelocity = this.getLinearVelocity();
        this.physicsStats.averageVelocity = 
            (this.physicsStats.averageVelocity + currentVelocity.length()) / 2;
    }
    
    /**
     * 序列化组件数据
     * @returns {Object} 序列化数据
     */
    onSerialize() {
        return {
            physicsConfig: this.physicsConfig,
            shapeOptions: {
                size: this.shapeOptions.size.asArray(),
                radius: this.shapeOptions.radius,
                height: this.shapeOptions.height
            },
            constraints: this.constraints,
            isPhysicsEnabled: this.isPhysicsEnabled,
            velocity: this.velocity.asArray(),
            angularVelocity: this.angularVelocity.asArray()
        };
    }
    
    /**
     * 反序列化组件数据
     * @param {Object} data - 序列化数据
     */
    onDeserialize(data) {
        if (data.physicsConfig) {
            this.physicsConfig = { ...this.physicsConfig, ...data.physicsConfig };
        }
        
        if (data.shapeOptions) {
            this.shapeOptions.size = Vector3.FromArray(data.shapeOptions.size);
            this.shapeOptions.radius = data.shapeOptions.radius;
            this.shapeOptions.height = data.shapeOptions.height;
        }
        
        if (data.constraints) {
            this.constraints = { ...this.constraints, ...data.constraints };
        }
        
        if (data.velocity) {
            this.velocity = Vector3.FromArray(data.velocity);
        }
        
        if (data.angularVelocity) {
            this.angularVelocity = Vector3.FromArray(data.angularVelocity);
        }
        
        // 重新创建物理体
        if (this.isInitialized) {
            this.createPhysicsBody();
        }
    }
    
    /**
     * 组件清理
     */
    onCleanup() {
        // 清理物理体
        if (this.physicsAggregate) {
            this.physicsAggregate.dispose();
            this.physicsAggregate = null;
        }
        
        // 清理回调
        this.collisionCallbacks.clear();
        this.triggerCallbacks.clear();
        
        this.isInitialized = false;
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            physicsConfig: this.physicsConfig,
            isPhysicsEnabled: this.isPhysicsEnabled,
            isInitialized: this.isInitialized,
            velocity: this.velocity.asArray(),
            angularVelocity: this.angularVelocity.asArray(),
            physicsStats: this.physicsStats,
            hasPhysicsBody: !!this.physicsAggregate
        };
    }
}

export default PhysicsComponent;
