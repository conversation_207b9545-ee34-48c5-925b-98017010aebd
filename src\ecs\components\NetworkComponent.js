// src/ecs/components/NetworkComponent.js
// 网络组件 - 管理实体的网络同步和通信

import Component from '../Component.js';

/**
 * 网络组件类
 * 管理实体的网络同步，包括位置、状态、属性的网络传输
 * 支持客户端预测、服务器权威、插值补偿等网络优化技术
 */
export class NetworkComponent extends Component {
    /**
     * 构造函数
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     */
    constructor(entity = null, options = {}) {
        super(entity, options);
        
        // 网络配置
        this.networkConfig = {
            networkId: options.networkId || null,          // 网络ID
            ownerId: options.ownerId || null,              // 所有者ID
            isLocalOwned: options.isLocalOwned || false,   // 是否本地拥有
            syncPosition: options.syncPosition !== false,  // 同步位置
            syncRotation: options.syncRotation !== false,  // 同步旋转
            syncScale: options.syncScale || false,         // 同步缩放
            syncAnimation: options.syncAnimation || false, // 同步动画
            syncPhysics: options.syncPhysics || false,     // 同步物理
            updateRate: options.updateRate || 20,          // 更新频率(Hz)
            interpolation: options.interpolation !== false, // 启用插值
            extrapolation: options.extrapolation || false, // 启用外推
            compressionLevel: options.compressionLevel || 1 // 压缩级别
        };
        
        // 同步状态
        this.syncState = {
            lastSyncTime: 0,
            lastSendTime: 0,
            syncInterval: 1000 / this.networkConfig.updateRate,
            needsSync: false,
            isReceiving: false
        };
        
        // 网络数据缓存
        this.networkData = {
            position: null,
            rotation: null,
            scale: null,
            velocity: null,
            angularVelocity: null,
            animationState: null,
            customData: new Map()
        };
        
        // 插值数据
        this.interpolationData = {
            enabled: this.networkConfig.interpolation,
            startPosition: null,
            targetPosition: null,
            startRotation: null,
            targetRotation: null,
            startTime: 0,
            duration: 0,
            progress: 0
        };
        
        // 预测数据（客户端预测）
        this.predictionData = {
            enabled: this.networkConfig.isLocalOwned,
            predictedPosition: null,
            predictedRotation: null,
            lastConfirmedPosition: null,
            lastConfirmedRotation: null,
            correctionThreshold: 0.1
        };
        
        // 网络统计
        this.networkStats = {
            bytesSent: 0,
            bytesReceived: 0,
            messagesSent: 0,
            messagesReceived: 0,
            lastRTT: 0,
            averageRTT: 0,
            packetLoss: 0,
            lastUpdateTime: 0
        };
        
        // 消息队列
        this.outgoingMessages = [];
        this.incomingMessages = [];
        
        console.log(`网络组件已创建: ${this.entity?.id}`);
    }
    
    /**
     * 组件初始化
     */
    onInitialize() {
        // 获取网络管理器
        this.networkManager = this.getNetworkManager();
        
        if (!this.networkManager) {
            console.warn('网络管理器不可用，网络同步将被禁用');
            return;
        }
        
        // 注册网络实体
        this.registerNetworkEntity();
        
        // 监听变换变化
        const transformComponent = this.getDependency('TransformComponent');
        if (transformComponent) {
            transformComponent.on('transformChanged', () => {
                this.onTransformChanged();
            });
        }
        
        // 监听动画变化
        const animationComponent = this.entity.getComponent('AnimationComponent');
        if (animationComponent) {
            animationComponent.on('animationStarted', (data) => {
                this.onAnimationChanged(data);
            });
        }
        
        // 设置网络消息监听
        this.setupNetworkListeners();
        
        this.emit('networkReady', { component: this });
    }
    
    /**
     * 获取网络管理器
     * @private
     * @returns {NetworkManager} 网络管理器实例
     */
    getNetworkManager() {
        // 这里需要根据实际的网络管理器获取方式来实现
        // 可能是全局单例、从场景获取、或从ECS管理器获取
        if (typeof window !== 'undefined' && window.networkManager) {
            return window.networkManager;
        }
        
        // 或者从实体的场景中获取
        if (this.entity?.scene?.networkManager) {
            return this.entity.scene.networkManager;
        }
        
        return null;
    }
    
    /**
     * 注册网络实体
     * @private
     */
    registerNetworkEntity() {
        if (!this.networkManager) return;
        
        // 生成网络ID（如果没有）
        if (!this.networkConfig.networkId) {
            this.networkConfig.networkId = this.generateNetworkId();
        }
        
        // 向网络管理器注册实体
        this.networkManager.registerNetworkEntity(this.networkConfig.networkId, this);
        
        console.log(`网络实体已注册: ${this.networkConfig.networkId}`);
    }
    
    /**
     * 生成网络ID
     * @private
     * @returns {string} 网络ID
     */
    generateNetworkId() {
        return `net_${this.entity.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 设置网络消息监听
     * @private
     */
    setupNetworkListeners() {
        if (!this.networkManager) return;
        
        // 监听网络消息
        this.networkManager.on('entityUpdate', (data) => {
            if (data.networkId === this.networkConfig.networkId) {
                this.handleNetworkUpdate(data);
            }
        });
        
        this.networkManager.on('entityDestroyed', (data) => {
            if (data.networkId === this.networkConfig.networkId) {
                this.handleNetworkDestroy(data);
            }
        });
    }
    
    /**
     * 变换改变时的处理
     * @private
     */
    onTransformChanged() {
        if (!this.networkConfig.isLocalOwned) return;
        
        // 标记需要同步
        this.syncState.needsSync = true;
        
        // 更新预测数据
        if (this.predictionData.enabled) {
            const transformComponent = this.getDependency('TransformComponent');
            if (transformComponent) {
                this.predictionData.predictedPosition = transformComponent.getPosition();
                this.predictionData.predictedRotation = transformComponent.rotation.clone();
            }
        }
    }
    
    /**
     * 动画改变时的处理
     * @private
     * @param {Object} data - 动画数据
     */
    onAnimationChanged(data) {
        if (!this.networkConfig.isLocalOwned || !this.networkConfig.syncAnimation) return;
        
        // 准备动画同步数据
        this.networkData.animationState = {
            name: data.name,
            startTime: performance.now(),
            options: data.options
        };
        
        this.syncState.needsSync = true;
    }
    
    /**
     * 发送网络更新
     * @private
     */
    sendNetworkUpdate() {
        if (!this.networkManager || !this.networkConfig.isLocalOwned) return;
        
        const currentTime = performance.now();
        
        // 检查是否需要发送更新
        if (currentTime - this.syncState.lastSendTime < this.syncState.syncInterval) {
            return;
        }
        
        // 收集同步数据
        const updateData = this.collectSyncData();
        
        if (!updateData) return;
        
        // 压缩数据
        const compressedData = this.compressData(updateData);
        
        // 发送网络消息
        const message = {
            type: 'entityUpdate',
            networkId: this.networkConfig.networkId,
            timestamp: currentTime,
            data: compressedData
        };
        
        this.networkManager.send(message);
        
        // 更新统计
        this.networkStats.messagesSent++;
        this.networkStats.bytesSent += JSON.stringify(message).length;
        this.syncState.lastSendTime = currentTime;
        this.syncState.needsSync = false;
        
        this.emit('networkSent', { component: this, message });
    }
    
    /**
     * 收集同步数据
     * @private
     * @returns {Object} 同步数据
     */
    collectSyncData() {
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) return null;
        
        const data = {
            timestamp: performance.now()
        };
        
        // 收集位置数据
        if (this.networkConfig.syncPosition) {
            data.position = transformComponent.getPosition().asArray();
        }
        
        // 收集旋转数据
        if (this.networkConfig.syncRotation) {
            data.rotation = transformComponent.rotation.asArray();
        }
        
        // 收集缩放数据
        if (this.networkConfig.syncScale) {
            data.scale = transformComponent.getScale().asArray();
        }
        
        // 收集物理数据
        if (this.networkConfig.syncPhysics) {
            const physicsComponent = this.entity.getComponent('PhysicsComponent');
            if (physicsComponent) {
                data.velocity = physicsComponent.getLinearVelocity().asArray();
                data.angularVelocity = physicsComponent.getAngularVelocity().asArray();
            }
        }
        
        // 收集动画数据
        if (this.networkConfig.syncAnimation && this.networkData.animationState) {
            data.animationState = this.networkData.animationState;
        }
        
        // 收集自定义数据
        if (this.networkData.customData.size > 0) {
            data.customData = Object.fromEntries(this.networkData.customData);
        }
        
        return data;
    }
    
    /**
     * 压缩数据
     * @private
     * @param {Object} data - 原始数据
     * @returns {Object} 压缩后的数据
     */
    compressData(data) {
        // 根据压缩级别进行数据压缩
        switch (this.networkConfig.compressionLevel) {
            case 0: // 无压缩
                return data;
                
            case 1: // 基础压缩 - 减少精度
                const compressed = { ...data };
                if (compressed.position) {
                    compressed.position = compressed.position.map(v => Math.round(v * 100) / 100);
                }
                if (compressed.rotation) {
                    compressed.rotation = compressed.rotation.map(v => Math.round(v * 1000) / 1000);
                }
                return compressed;
                
            case 2: // 高级压缩 - 差值编码
                // 这里可以实现更复杂的压缩算法
                return this.deltaCompress(data);
                
            default:
                return data;
        }
    }
    
    /**
     * 差值压缩
     * @private
     * @param {Object} data - 数据
     * @returns {Object} 压缩后的数据
     */
    deltaCompress(data) {
        // 简化的差值压缩实现
        // 实际实现需要维护上一次发送的数据状态
        return data;
    }
    
    /**
     * 处理网络更新
     * @private
     * @param {Object} data - 网络数据
     */
    handleNetworkUpdate(data) {
        if (this.networkConfig.isLocalOwned) return; // 忽略自己的更新
        
        this.syncState.isReceiving = true;
        
        // 解压数据
        const decompressedData = this.decompressData(data.data);
        
        // 应用网络数据
        this.applyNetworkData(decompressedData);
        
        // 更新统计
        this.networkStats.messagesReceived++;
        this.networkStats.bytesReceived += JSON.stringify(data).length;
        this.syncState.lastSyncTime = performance.now();
        
        // 计算RTT
        if (data.timestamp) {
            const rtt = performance.now() - data.timestamp;
            this.networkStats.lastRTT = rtt;
            this.networkStats.averageRTT = (this.networkStats.averageRTT + rtt) / 2;
        }
        
        this.syncState.isReceiving = false;
        
        this.emit('networkReceived', { component: this, data: decompressedData });
    }
    
    /**
     * 解压数据
     * @private
     * @param {Object} data - 压缩的数据
     * @returns {Object} 解压后的数据
     */
    decompressData(data) {
        // 根据压缩级别进行数据解压
        // 这里需要与压缩方法对应
        return data;
    }
    
    /**
     * 应用网络数据
     * @private
     * @param {Object} data - 网络数据
     */
    applyNetworkData(data) {
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) return;
        
        // 应用位置数据
        if (data.position && this.networkConfig.syncPosition) {
            const newPosition = new (require('@babylonjs/core').Vector3)(...data.position);
            
            if (this.interpolationData.enabled) {
                this.startInterpolation('position', transformComponent.getPosition(), newPosition);
            } else {
                transformComponent.setPosition(newPosition);
            }
        }
        
        // 应用旋转数据
        if (data.rotation && this.networkConfig.syncRotation) {
            const newRotation = new (require('@babylonjs/core').Quaternion)(...data.rotation);
            
            if (this.interpolationData.enabled) {
                this.startInterpolation('rotation', transformComponent.rotation, newRotation);
            } else {
                transformComponent.setRotation(newRotation);
            }
        }
        
        // 应用缩放数据
        if (data.scale && this.networkConfig.syncScale) {
            const newScale = new (require('@babylonjs/core').Vector3)(...data.scale);
            transformComponent.setScale(newScale);
        }
        
        // 应用物理数据
        if (this.networkConfig.syncPhysics) {
            const physicsComponent = this.entity.getComponent('PhysicsComponent');
            if (physicsComponent) {
                if (data.velocity) {
                    const velocity = new (require('@babylonjs/core').Vector3)(...data.velocity);
                    physicsComponent.setLinearVelocity(velocity);
                }
                if (data.angularVelocity) {
                    const angularVelocity = new (require('@babylonjs/core').Vector3)(...data.angularVelocity);
                    physicsComponent.setAngularVelocity(angularVelocity);
                }
            }
        }
        
        // 应用动画数据
        if (data.animationState && this.networkConfig.syncAnimation) {
            const animationComponent = this.entity.getComponent('AnimationComponent');
            if (animationComponent) {
                animationComponent.play(data.animationState.name, data.animationState.options);
            }
        }
        
        // 应用自定义数据
        if (data.customData) {
            Object.entries(data.customData).forEach(([key, value]) => {
                this.networkData.customData.set(key, value);
            });
        }
    }
    
    /**
     * 开始插值
     * @private
     * @param {string} type - 插值类型
     * @param {*} start - 起始值
     * @param {*} target - 目标值
     */
    startInterpolation(type, start, target) {
        if (type === 'position') {
            this.interpolationData.startPosition = start.clone();
            this.interpolationData.targetPosition = target.clone();
        } else if (type === 'rotation') {
            this.interpolationData.startRotation = start.clone();
            this.interpolationData.targetRotation = target.clone();
        }
        
        this.interpolationData.startTime = performance.now();
        this.interpolationData.duration = this.syncState.syncInterval;
        this.interpolationData.progress = 0;
    }
    
    /**
     * 更新插值
     * @private
     * @param {number} deltaTime - 时间间隔
     */
    updateInterpolation(deltaTime) {
        if (!this.interpolationData.enabled) return;
        
        const currentTime = performance.now();
        const elapsed = currentTime - this.interpolationData.startTime;
        this.interpolationData.progress = Math.min(elapsed / this.interpolationData.duration, 1.0);
        
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) return;
        
        // 插值位置
        if (this.interpolationData.startPosition && this.interpolationData.targetPosition) {
            const { Vector3 } = require('@babylonjs/core');
            const currentPos = Vector3.Lerp(
                this.interpolationData.startPosition,
                this.interpolationData.targetPosition,
                this.interpolationData.progress
            );
            transformComponent.setPosition(currentPos);
        }
        
        // 插值旋转
        if (this.interpolationData.startRotation && this.interpolationData.targetRotation) {
            const { Quaternion } = require('@babylonjs/core');
            const currentRot = Quaternion.Slerp(
                this.interpolationData.startRotation,
                this.interpolationData.targetRotation,
                this.interpolationData.progress
            );
            transformComponent.setRotation(currentRot);
        }
        
        // 插值完成
        if (this.interpolationData.progress >= 1.0) {
            this.interpolationData.startPosition = null;
            this.interpolationData.targetPosition = null;
            this.interpolationData.startRotation = null;
            this.interpolationData.targetRotation = null;
        }
    }
    
    /**
     * 设置自定义网络数据
     * @param {string} key - 数据键
     * @param {*} value - 数据值
     */
    setCustomData(key, value) {
        this.networkData.customData.set(key, value);
        this.syncState.needsSync = true;
    }
    
    /**
     * 获取自定义网络数据
     * @param {string} key - 数据键
     * @returns {*} 数据值
     */
    getCustomData(key) {
        return this.networkData.customData.get(key);
    }
    
    /**
     * 设置网络所有权
     * @param {boolean} isOwned - 是否拥有
     * @param {string} [ownerId] - 所有者ID
     */
    setNetworkOwnership(isOwned, ownerId = null) {
        this.networkConfig.isLocalOwned = isOwned;
        this.networkConfig.ownerId = ownerId;
        this.predictionData.enabled = isOwned;
        
        this.emit('ownershipChanged', { 
            component: this, 
            isOwned, 
            ownerId 
        });
    }
    
    /**
     * 组件更新
     * @param {number} deltaTime - 时间间隔
     */
    onUpdate(deltaTime) {
        // 发送网络更新
        if (this.syncState.needsSync) {
            this.sendNetworkUpdate();
        }
        
        // 更新插值
        if (!this.networkConfig.isLocalOwned) {
            this.updateInterpolation(deltaTime);
        }
        
        // 更新统计
        this.networkStats.lastUpdateTime = performance.now();
    }
    
    /**
     * 处理网络销毁
     * @private
     * @param {Object} data - 销毁数据
     */
    handleNetworkDestroy(data) {
        // 销毁实体
        if (this.entity && !this.entity.isDestroyed) {
            this.entity.destroy();
        }
        
        this.emit('networkDestroyed', { component: this, data });
    }
    
    /**
     * 序列化组件数据
     * @returns {Object} 序列化数据
     */
    onSerialize() {
        return {
            networkConfig: this.networkConfig,
            networkData: {
                customData: Object.fromEntries(this.networkData.customData)
            }
        };
    }
    
    /**
     * 反序列化组件数据
     * @param {Object} data - 序列化数据
     */
    onDeserialize(data) {
        if (data.networkConfig) {
            this.networkConfig = { ...this.networkConfig, ...data.networkConfig };
        }
        
        if (data.networkData && data.networkData.customData) {
            this.networkData.customData = new Map(Object.entries(data.networkData.customData));
        }
    }
    
    /**
     * 组件清理
     */
    onCleanup() {
        // 注销网络实体
        if (this.networkManager && this.networkConfig.networkId) {
            this.networkManager.unregisterNetworkEntity(this.networkConfig.networkId);
        }
        
        // 清理数据
        this.networkData.customData.clear();
        this.outgoingMessages.length = 0;
        this.incomingMessages.length = 0;
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            networkConfig: this.networkConfig,
            syncState: this.syncState,
            interpolationData: this.interpolationData,
            predictionData: this.predictionData,
            networkStats: this.networkStats,
            customDataCount: this.networkData.customData.size
        };
    }
}

export default NetworkComponent;
