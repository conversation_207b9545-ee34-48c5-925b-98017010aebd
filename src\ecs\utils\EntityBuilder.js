// src/ecs/utils/EntityBuilder.js
// 实体构建器 - 提供流畅的实体创建和配置API

import { Vector3, Color3, Quaternion } from '@babylonjs/core';
import componentFactory from './ComponentFactory.js';

/**
 * 实体构建器类
 * 提供链式调用的实体创建和组件配置API
 */
export class EntityBuilder {
    /**
     * 构造函数
     * @param {ECSManager} ecsManager - ECS管理器
     */
    constructor(ecsManager) {
        this.ecsManager = ecsManager;
        this.reset();
        
        console.log('实体构建器已创建');
    }
    
    /**
     * 重置构建器状态
     * @private
     */
    reset() {
        this.entity = null;
        this.componentConfigs = [];
        this.tags = [];
        this.metadata = new Map();
        this.parentEntity = null;
        this.childEntities = [];
    }
    
    /**
     * 开始构建新实体
     * @param {string} [name=''] - 实体名称
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    create(name = '') {
        this.reset();
        this.entity = this.ecsManager.createEntity(name);
        
        if (!this.entity) {
            throw new Error('无法创建实体');
        }
        
        return this;
    }
    
    /**
     * 使用预设创建实体
     * @param {string} presetName - 预设名称
     * @param {string} [name=''] - 实体名称
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    fromPreset(presetName, name = '') {
        this.create(name);
        
        const preset = componentFactory.getPreset(presetName);
        if (!preset) {
            throw new Error(`未知的预设: ${presetName}`);
        }
        
        // 添加预设中的组件配置
        preset.components.forEach(config => {
            this.componentConfigs.push(config);
        });
        
        return this;
    }
    
    /**
     * 设置实体位置
     * @param {number|Vector3} x - X坐标或Vector3对象
     * @param {number} [y] - Y坐标
     * @param {number} [z] - Z坐标
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    at(x, y, z) {
        let position;
        if (x instanceof Vector3) {
            position = x.clone();
        } else {
            position = new Vector3(x, y, z);
        }
        
        // 查找或创建TransformComponent配置
        let transformConfig = this.componentConfigs.find(c => c.type === 'TransformComponent');
        if (!transformConfig) {
            transformConfig = { type: 'TransformComponent', options: {} };
            this.componentConfigs.push(transformConfig);
        }
        
        transformConfig.options.position = position;
        
        return this;
    }
    
    /**
     * 设置实体旋转
     * @param {number|Vector3|Quaternion} x - X轴旋转角度、欧拉角向量或四元数
     * @param {number} [y] - Y轴旋转角度
     * @param {number} [z] - Z轴旋转角度
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    rotatedBy(x, y, z) {
        let rotation;
        
        if (x instanceof Quaternion) {
            rotation = x.clone();
        } else if (x instanceof Vector3) {
            rotation = Quaternion.FromEulerAngles(x.x, x.y, x.z);
        } else {
            rotation = Quaternion.FromEulerAngles(x, y, z);
        }
        
        // 查找或创建TransformComponent配置
        let transformConfig = this.componentConfigs.find(c => c.type === 'TransformComponent');
        if (!transformConfig) {
            transformConfig = { type: 'TransformComponent', options: {} };
            this.componentConfigs.push(transformConfig);
        }
        
        transformConfig.options.rotation = rotation;
        
        return this;
    }
    
    /**
     * 设置实体缩放
     * @param {number|Vector3} x - X轴缩放或Vector3缩放或统一缩放
     * @param {number} [y] - Y轴缩放
     * @param {number} [z] - Z轴缩放
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    scaledBy(x, y, z) {
        let scale;
        
        if (x instanceof Vector3) {
            scale = x.clone();
        } else if (typeof x === 'number' && y === undefined && z === undefined) {
            scale = new Vector3(x, x, x);
        } else {
            scale = new Vector3(x, y, z);
        }
        
        // 查找或创建TransformComponent配置
        let transformConfig = this.componentConfigs.find(c => c.type === 'TransformComponent');
        if (!transformConfig) {
            transformConfig = { type: 'TransformComponent', options: {} };
            this.componentConfigs.push(transformConfig);
        }
        
        transformConfig.options.scale = scale;
        
        return this;
    }
    
    /**
     * 添加渲染组件
     * @param {Object} [options={}] - 渲染选项
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withRender(options = {}) {
        this.componentConfigs.push({
            type: 'RenderComponent',
            options
        });
        
        return this;
    }
    
    /**
     * 设置网格类型
     * @param {string} meshType - 网格类型
     * @param {Object} [meshOptions={}] - 网格选项
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withMesh(meshType, meshOptions = {}) {
        let renderConfig = this.componentConfigs.find(c => c.type === 'RenderComponent');
        if (!renderConfig) {
            renderConfig = { type: 'RenderComponent', options: {} };
            this.componentConfigs.push(renderConfig);
        }
        
        renderConfig.options.meshType = meshType;
        renderConfig.options.meshOptions = meshOptions;
        
        return this;
    }
    
    /**
     * 设置颜色
     * @param {Color3|string} color - 颜色对象或颜色字符串
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withColor(color) {
        let finalColor;
        
        if (typeof color === 'string') {
            finalColor = Color3.FromHexString(color);
        } else if (color instanceof Color3) {
            finalColor = color.clone();
        } else {
            finalColor = new Color3(color.r || 1, color.g || 1, color.b || 1);
        }
        
        let renderConfig = this.componentConfigs.find(c => c.type === 'RenderComponent');
        if (!renderConfig) {
            renderConfig = { type: 'RenderComponent', options: {} };
            this.componentConfigs.push(renderConfig);
        }
        
        renderConfig.options.diffuseColor = finalColor;
        
        return this;
    }
    
    /**
     * 添加物理组件
     * @param {Object} [options={}] - 物理选项
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withPhysics(options = {}) {
        this.componentConfigs.push({
            type: 'PhysicsComponent',
            options
        });
        
        return this;
    }
    
    /**
     * 设置为静态物理体
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    asStatic() {
        let physicsConfig = this.componentConfigs.find(c => c.type === 'PhysicsComponent');
        if (!physicsConfig) {
            physicsConfig = { type: 'PhysicsComponent', options: {} };
            this.componentConfigs.push(physicsConfig);
        }
        
        physicsConfig.options.isStatic = true;
        physicsConfig.options.mass = 0;
        
        return this;
    }
    
    /**
     * 设置为触发器
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    asTrigger() {
        let physicsConfig = this.componentConfigs.find(c => c.type === 'PhysicsComponent');
        if (!physicsConfig) {
            physicsConfig = { type: 'PhysicsComponent', options: {} };
            this.componentConfigs.push(physicsConfig);
        }
        
        physicsConfig.options.isTrigger = true;
        physicsConfig.options.mass = 0;
        
        return this;
    }
    
    /**
     * 设置质量
     * @param {number} mass - 质量
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withMass(mass) {
        let physicsConfig = this.componentConfigs.find(c => c.type === 'PhysicsComponent');
        if (!physicsConfig) {
            physicsConfig = { type: 'PhysicsComponent', options: {} };
            this.componentConfigs.push(physicsConfig);
        }
        
        physicsConfig.options.mass = mass;
        
        return this;
    }
    
    /**
     * 添加动画组件
     * @param {Object} [options={}] - 动画选项
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withAnimation(options = {}) {
        this.componentConfigs.push({
            type: 'AnimationComponent',
            options
        });
        
        return this;
    }
    
    /**
     * 设置自动播放动画
     * @param {string} animationName - 动画名称
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    autoPlay(animationName) {
        let animationConfig = this.componentConfigs.find(c => c.type === 'AnimationComponent');
        if (!animationConfig) {
            animationConfig = { type: 'AnimationComponent', options: {} };
            this.componentConfigs.push(animationConfig);
        }
        
        animationConfig.options.autoPlay = animationName;
        
        return this;
    }
    
    /**
     * 添加网络组件
     * @param {Object} [options={}] - 网络选项
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withNetwork(options = {}) {
        this.componentConfigs.push({
            type: 'NetworkComponent',
            options
        });
        
        return this;
    }
    
    /**
     * 设置为本地拥有
     * @param {string} [ownerId] - 所有者ID
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    asLocalOwned(ownerId = null) {
        let networkConfig = this.componentConfigs.find(c => c.type === 'NetworkComponent');
        if (!networkConfig) {
            networkConfig = { type: 'NetworkComponent', options: {} };
            this.componentConfigs.push(networkConfig);
        }
        
        networkConfig.options.isLocalOwned = true;
        if (ownerId) {
            networkConfig.options.ownerId = ownerId;
        }
        
        return this;
    }
    
    /**
     * 添加自定义组件
     * @param {string} componentType - 组件类型
     * @param {Object} [options={}] - 组件选项
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withComponent(componentType, options = {}) {
        this.componentConfigs.push({
            type: componentType,
            options
        });
        
        return this;
    }
    
    /**
     * 添加标签
     * @param {...string} tags - 标签列表
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withTags(...tags) {
        this.tags.push(...tags);
        return this;
    }
    
    /**
     * 设置元数据
     * @param {string} key - 键
     * @param {*} value - 值
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withMetadata(key, value) {
        this.metadata.set(key, value);
        return this;
    }
    
    /**
     * 设置父实体
     * @param {Entity} parent - 父实体
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withParent(parent) {
        this.parentEntity = parent;
        return this;
    }
    
    /**
     * 添加子实体
     * @param {Entity} child - 子实体
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    withChild(child) {
        this.childEntities.push(child);
        return this;
    }
    
    /**
     * 设置可见性
     * @param {boolean} visible - 是否可见
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    visible(visible = true) {
        let renderConfig = this.componentConfigs.find(c => c.type === 'RenderComponent');
        if (!renderConfig) {
            renderConfig = { type: 'RenderComponent', options: {} };
            this.componentConfigs.push(renderConfig);
        }
        
        renderConfig.options.visible = visible;
        
        return this;
    }
    
    /**
     * 设置为不可见
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    hidden() {
        return this.visible(false);
    }
    
    /**
     * 应用配置函数
     * @param {Function} configFn - 配置函数
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    configure(configFn) {
        configFn(this);
        return this;
    }
    
    /**
     * 条件性配置
     * @param {boolean} condition - 条件
     * @param {Function} configFn - 配置函数
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    when(condition, configFn) {
        if (condition) {
            configFn(this);
        }
        return this;
    }
    
    /**
     * 构建并返回实体
     * @returns {Entity} 构建的实体
     */
    build() {
        if (!this.entity) {
            throw new Error('必须先调用create()方法');
        }
        
        // 创建并添加组件
        this.componentConfigs.forEach(config => {
            const component = componentFactory.createComponent(config.type, this.entity, config.options);
            if (component) {
                this.entity.addComponent(component);
            }
        });
        
        // 添加标签
        this.tags.forEach(tag => {
            this.entity.addTag(tag);
        });
        
        // 设置元数据
        this.metadata.forEach((value, key) => {
            this.entity.setMetadata(key, value);
        });
        
        // 设置父子关系
        if (this.parentEntity) {
            this.parentEntity.addChild(this.entity);
        }
        
        this.childEntities.forEach(child => {
            this.entity.addChild(child);
        });
        
        const builtEntity = this.entity;
        
        // 重置构建器状态
        this.reset();
        
        console.log(`实体构建完成: ${builtEntity.id}`);
        
        return builtEntity;
    }
    
    /**
     * 构建多个相似实体
     * @param {number} count - 数量
     * @param {Function} [modifierFn] - 修改函数
     * @returns {Array<Entity>} 实体数组
     */
    buildMultiple(count, modifierFn = null) {
        const entities = [];
        
        for (let i = 0; i < count; i++) {
            // 保存当前配置
            const savedConfigs = JSON.parse(JSON.stringify(this.componentConfigs));
            const savedTags = [...this.tags];
            const savedMetadata = new Map(this.metadata);
            
            // 创建新实体
            this.create(`${this.entity?.name || 'entity'}_${i}`);
            
            // 恢复配置
            this.componentConfigs = JSON.parse(JSON.stringify(savedConfigs));
            this.tags = [...savedTags];
            this.metadata = new Map(savedMetadata);
            
            // 应用修改函数
            if (modifierFn) {
                modifierFn(this, i);
            }
            
            // 构建实体
            entities.push(this.build());
        }
        
        return entities;
    }
    
    /**
     * 克隆现有实体
     * @param {Entity} sourceEntity - 源实体
     * @param {string} [name] - 新实体名称
     * @returns {EntityBuilder} 返回自身，支持链式调用
     */
    clone(sourceEntity, name = null) {
        this.create(name || `${sourceEntity.name}_clone`);
        
        // 复制组件配置
        sourceEntity.getAllComponents().forEach(component => {
            const serializedData = component.serialize();
            this.componentConfigs.push({
                type: component.constructor.name,
                options: serializedData.options || {}
            });
        });
        
        // 复制标签
        this.tags = sourceEntity.getTags();
        
        // 复制元数据
        sourceEntity.metadata.forEach((value, key) => {
            this.metadata.set(key, value);
        });
        
        return this;
    }
    
    /**
     * 获取当前配置的调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            entityId: this.entity?.id,
            entityName: this.entity?.name,
            componentConfigs: this.componentConfigs,
            tags: this.tags,
            metadata: Object.fromEntries(this.metadata),
            hasParent: !!this.parentEntity,
            childrenCount: this.childEntities.length
        };
    }
}

export default EntityBuilder;
