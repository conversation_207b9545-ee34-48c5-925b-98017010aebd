// src/entities/ecsPlayer.js
// ECS版本的玩家实体 - 使用组件化架构

import { Vector3, Color3 } from '@babylonjs/core';
import { ENTITY_PRESETS } from '../ecs/index.js';

/**
 * ECS玩家实体工厂
 * 使用ECS架构创建和管理玩家实体
 */
export class ECSPlayerFactory {
    constructor(ecsManager) {
        this.ecsManager = ecsManager;
        this.players = new Map(); // playerId -> entity
    }
    
    /**
     * 创建玩家实体
     * @param {string} playerId - 玩家ID
     * @param {Vector3} position - 初始位置
     * @param {boolean} isLocalPlayer - 是否为本地玩家
     * @returns {Entity} 玩家实体
     */
    createPlayer(playerId, position = Vector3.Zero(), isLocalPlayer = false) {
        // 如果玩家已存在，先移除
        if (this.players.has(playerId)) {
            this.removePlayer(playerId);
        }
        
        // 使用实体构建器创建玩家
        const player = this.ecsManager.builder
            .create(`player_${playerId}`)
            .fromPreset(ENTITY_PRESETS.PLAYER)
            .at(position.x, position.y + 1.25, position.z) // 胶囊体中心位置
            .withRender({
                meshType: 'capsule',
                meshOptions: { height: 2.5, radius: 0.5 },
                diffuseColor: isLocalPlayer ? 
                    new Color3(0.2, 0.4, 0.8) : // 本地玩家蓝色
                    new Color3(0.5, 0.5, 0.5),  // 远程玩家灰色
                emissiveColor: isLocalPlayer ? 
                    new Color3(0.1, 0.2, 0.4) : 
                    new Color3(0, 0, 0)
            })
            .withPhysics({
                type: 'capsule',
                mass: 70,
                friction: 0.5,
                restitution: 0.1,
                height: 2.5,
                radius: 0.5,
                freezeRotation: true // 防止玩家倒下
            })
            .withAnimation({
                autoPlay: 'idle',
                enableBlending: true
            })
            .withNetwork({
                isLocalOwned: isLocalPlayer,
                ownerId: playerId,
                syncPosition: true,
                syncRotation: true,
                updateRate: isLocalPlayer ? 20 : 10
            })
            .withTags('player', isLocalPlayer ? 'local' : 'remote')
            .withMetadata('playerId', playerId)
            .withMetadata('isLocalPlayer', isLocalPlayer)
            .withMetadata('movementSpeed', 4.0)
            .withMetadata('rotationSpeed', Math.PI * 3)
            .withMetadata('health', 100)
            .withMetadata('maxHealth', 100)
            .build();
        
        // 添加自定义玩家组件
        this.addPlayerComponents(player, isLocalPlayer);
        
        // 存储玩家引用
        this.players.set(playerId, player);
        
        console.log(`ECS玩家已创建: ${playerId} (${isLocalPlayer ? '本地' : '远程'})`);
        
        return player;
    }
    
    /**
     * 添加自定义玩家组件
     * @param {Entity} player - 玩家实体
     * @param {boolean} isLocalPlayer - 是否为本地玩家
     */
    addPlayerComponents(player, isLocalPlayer) {
        // 添加玩家控制组件
        if (isLocalPlayer) {
            const playerController = new PlayerControllerComponent(player, {
                movementSpeed: player.getMetadata('movementSpeed'),
                rotationSpeed: player.getMetadata('rotationSpeed')
            });
            player.addComponent(playerController);
        }
        
        // 添加健康组件
        const healthComponent = new PlayerHealthComponent(player, {
            maxHealth: player.getMetadata('maxHealth'),
            currentHealth: player.getMetadata('health'),
            regenerationRate: 1.0 // 每秒恢复1点生命值
        });
        player.addComponent(healthComponent);
        
        // 添加状态组件
        const stateComponent = new PlayerStateComponent(player, {
            state: 'idle',
            canMove: true,
            canJump: true
        });
        player.addComponent(stateComponent);
    }
    
    /**
     * 获取玩家实体
     * @param {string} playerId - 玩家ID
     * @returns {Entity|null} 玩家实体
     */
    getPlayer(playerId) {
        return this.players.get(playerId) || null;
    }
    
    /**
     * 移除玩家
     * @param {string} playerId - 玩家ID
     * @returns {boolean} 是否成功移除
     */
    removePlayer(playerId) {
        const player = this.players.get(playerId);
        if (player) {
            player.destroy();
            this.players.delete(playerId);
            console.log(`ECS玩家已移除: ${playerId}`);
            return true;
        }
        return false;
    }
    
    /**
     * 获取所有玩家
     * @returns {Array<Entity>} 所有玩家实体
     */
    getAllPlayers() {
        return Array.from(this.players.values());
    }
    
    /**
     * 获取本地玩家
     * @returns {Entity|null} 本地玩家实体
     */
    getLocalPlayer() {
        for (const player of this.players.values()) {
            if (player.getMetadata('isLocalPlayer')) {
                return player;
            }
        }
        return null;
    }
    
    /**
     * 清理所有玩家
     */
    cleanup() {
        this.players.forEach((player, playerId) => {
            player.destroy();
        });
        this.players.clear();
        console.log('所有ECS玩家已清理');
    }
}

/**
 * 玩家控制组件
 * 处理本地玩家的输入和移动
 */
export class PlayerControllerComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        
        this.dependencies = ['TransformComponent', 'PhysicsComponent'];
        
        this.movementSpeed = options.movementSpeed || 4.0;
        this.rotationSpeed = options.rotationSpeed || Math.PI * 3;
        
        this.currentMovementInput = Vector3.Zero();
        this.desiredRotationY = 0;
        
        // 输入状态
        this.inputState = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false
        };
    }
    
    onInitialize() {
        // 设置输入监听
        this.setupInputListeners();
    }
    
    setupInputListeners() {
        const scene = this.entity.scene || this.entity.getComponent('RenderComponent')?.mesh?.getScene();
        if (!scene) return;
        
        // 键盘输入监听
        scene.onKeyboardObservable.add((kbInfo) => {
            const key = kbInfo.event.key.toLowerCase();
            const isPressed = kbInfo.type === 1; // KEYDOWN
            
            switch (key) {
                case 'w':
                case 'arrowup':
                    this.inputState.forward = isPressed;
                    break;
                case 's':
                case 'arrowdown':
                    this.inputState.backward = isPressed;
                    break;
                case 'a':
                case 'arrowleft':
                    this.inputState.left = isPressed;
                    break;
                case 'd':
                case 'arrowright':
                    this.inputState.right = isPressed;
                    break;
                case ' ':
                    this.inputState.jump = isPressed;
                    break;
            }
        });
    }
    
    onUpdate(deltaTime) {
        this.updateMovement(deltaTime);
    }
    
    updateMovement(deltaTime) {
        const transform = this.getDependency('TransformComponent');
        const physics = this.getDependency('PhysicsComponent');
        
        if (!transform || !physics) return;
        
        // 计算移动输入向量
        const inputVector = Vector3.Zero();
        
        if (this.inputState.forward) inputVector.z += 1;
        if (this.inputState.backward) inputVector.z -= 1;
        if (this.inputState.left) inputVector.x -= 1;
        if (this.inputState.right) inputVector.x += 1;
        
        // 应用移动
        this.applyMovementInput(inputVector);
        
        // 更新物理和旋转
        if (this.currentMovementInput.lengthSquared() > 0.001) {
            // 获取当前速度，保留Y轴速度
            const currentVelocity = physics.getLinearVelocity();
            const targetHorizontalVelocity = this.currentMovementInput.normalize().scale(this.movementSpeed);
            
            // 设置新速度
            physics.setLinearVelocity(new Vector3(
                targetHorizontalVelocity.x,
                currentVelocity.y,
                targetHorizontalVelocity.z
            ));
            
            // 平滑旋转
            const currentRotation = transform.getEulerAngles();
            const targetRotationY = this.desiredRotationY;
            const newRotationY = this.lerpAngle(currentRotation.y, targetRotationY, this.rotationSpeed * deltaTime);
            
            transform.setRotationFromEuler(0, newRotationY, 0);
        } else {
            // 停止水平移动
            const currentVelocity = physics.getLinearVelocity();
            physics.setLinearVelocity(new Vector3(0, currentVelocity.y, 0));
        }
        
        // 处理跳跃
        if (this.inputState.jump) {
            this.jump();
        }
    }
    
    applyMovementInput(inputVector) {
        if (inputVector.lengthSquared() > 0.001) {
            this.currentMovementInput.copyFrom(inputVector.normalize());
            this.desiredRotationY = Math.atan2(inputVector.x, inputVector.z);
        } else {
            this.currentMovementInput.copyFrom(Vector3.Zero());
        }
    }
    
    jump() {
        const physics = this.getDependency('PhysicsComponent');
        if (!physics) return;
        
        // 简单的跳跃实现
        const currentVelocity = physics.getLinearVelocity();
        if (Math.abs(currentVelocity.y) < 0.1) { // 只有在接近地面时才能跳跃
            physics.setLinearVelocity(new Vector3(currentVelocity.x, 8, currentVelocity.z));
        }
    }
    
    lerpAngle(from, to, t) {
        // 角度插值，处理360度边界
        let diff = to - from;
        while (diff > Math.PI) diff -= 2 * Math.PI;
        while (diff < -Math.PI) diff += 2 * Math.PI;
        return from + diff * Math.min(t, 1);
    }
}

/**
 * 玩家健康组件
 */
export class PlayerHealthComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        
        this.maxHealth = options.maxHealth || 100;
        this.currentHealth = options.currentHealth || this.maxHealth;
        this.regenerationRate = options.regenerationRate || 0;
        this.isInvulnerable = false;
        this.lastDamageTime = 0;
    }
    
    onUpdate(deltaTime) {
        // 生命值恢复
        if (this.regenerationRate > 0 && this.currentHealth < this.maxHealth) {
            this.heal(this.regenerationRate * deltaTime);
        }
    }
    
    takeDamage(amount) {
        if (this.isInvulnerable) return;
        
        const oldHealth = this.currentHealth;
        this.currentHealth = Math.max(0, this.currentHealth - amount);
        this.lastDamageTime = performance.now();
        
        this.emit('healthChanged', {
            current: this.currentHealth,
            max: this.maxHealth,
            damage: amount,
            percentage: this.currentHealth / this.maxHealth
        });
        
        if (this.currentHealth === 0 && oldHealth > 0) {
            this.emit('death', { entity: this.entity });
        }
    }
    
    heal(amount) {
        const oldHealth = this.currentHealth;
        this.currentHealth = Math.min(this.maxHealth, this.currentHealth + amount);
        
        if (this.currentHealth !== oldHealth) {
            this.emit('healthChanged', {
                current: this.currentHealth,
                max: this.maxHealth,
                healing: amount,
                percentage: this.currentHealth / this.maxHealth
            });
        }
    }
    
    setInvulnerable(invulnerable, duration = 0) {
        this.isInvulnerable = invulnerable;
        
        if (invulnerable && duration > 0) {
            setTimeout(() => {
                this.isInvulnerable = false;
            }, duration);
        }
    }
}

/**
 * 玩家状态组件
 */
export class PlayerStateComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        
        this.state = options.state || 'idle';
        this.canMove = options.canMove !== false;
        this.canJump = options.canJump !== false;
        this.canAttack = options.canAttack !== false;
        
        this.stateHistory = [];
        this.maxHistoryLength = 10;
    }
    
    setState(newState) {
        if (this.state === newState) return;
        
        const oldState = this.state;
        this.state = newState;
        
        // 记录状态历史
        this.stateHistory.push({
            from: oldState,
            to: newState,
            timestamp: performance.now()
        });
        
        // 限制历史长度
        if (this.stateHistory.length > this.maxHistoryLength) {
            this.stateHistory.shift();
        }
        
        this.emit('stateChanged', {
            from: oldState,
            to: newState,
            entity: this.entity
        });
        
        // 更新动画
        this.updateAnimation();
    }
    
    updateAnimation() {
        const animation = this.entity.getComponent('AnimationComponent');
        if (!animation) return;
        
        switch (this.state) {
            case 'idle':
                animation.play('idle', { loop: true });
                break;
            case 'walking':
                animation.play('walk', { loop: true });
                break;
            case 'running':
                animation.play('run', { loop: true });
                break;
            case 'jumping':
                animation.play('jump', { loop: false });
                break;
            case 'dead':
                animation.play('death', { loop: false });
                break;
        }
    }
    
    canPerformAction(action) {
        switch (action) {
            case 'move':
                return this.canMove && this.state !== 'dead';
            case 'jump':
                return this.canJump && this.state !== 'dead';
            case 'attack':
                return this.canAttack && this.state !== 'dead';
            default:
                return false;
        }
    }
}

// 导入Component基类
import { Component } from '../ecs/index.js';

export default ECSPlayerFactory;
