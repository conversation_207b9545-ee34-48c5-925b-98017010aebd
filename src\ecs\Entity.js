// src/ecs/Entity.js
// 实体组件系统 - 实体基类
// 实体是游戏世界中的基本对象，通过组合不同的组件来定义其行为和属性

import { EventEmitter } from '../utils/eventEmitter.js';

/**
 * 实体类 - ECS架构中的实体基类
 * 实体本身不包含任何游戏逻辑，只是组件的容器
 * 通过添加不同的组件来定义实体的行为和属性
 */
export class Entity extends EventEmitter {
    /**
     * 构造函数
     * @param {string} id - 实体的唯一标识符
     * @param {string} [name=''] - 实体的可读名称
     */
    constructor(id, name = '') {
        super();

        // 实体的唯一标识符
        this.id = id;

        // 实体的可读名称，用于调试和显示
        this.name = name || id;

        // 组件存储映射 - 使用组件类型名作为键
        this.components = new Map();

        // 实体状态
        this.isActive = true;
        this.isDestroyed = false;

        // 创建时间戳
        this.createdAt = Date.now();

        // 标签系统 - 用于快速分类和查询实体
        this.tags = new Set();

        // 父子关系
        this.parent = null;
        this.children = new Set();

        // 元数据存储
        this.metadata = new Map();

        console.log(`实体已创建: ${this.id} (${this.name})`);
    }

    /**
     * 添加组件到实体
     * @param {Component} component - 要添加的组件实例
     * @returns {Entity} 返回自身，支持链式调用
     */
    addComponent(component) {
        if (this.isDestroyed) {
            console.warn(`尝试向已销毁的实体添加组件: ${this.id}`);
            return this;
        }

        if (!component) {
            console.error(`尝试添加空组件到实体: ${this.id}`);
            return this;
        }

        const componentType = component.constructor.name;

        // 检查是否已存在相同类型的组件
        if (this.components.has(componentType)) {
            console.warn(`实体 ${this.id} 已存在组件类型: ${componentType}`);
            return this;
        }

        // 设置组件的实体引用
        component.entity = this;

        // 存储组件
        this.components.set(componentType, component);

        // 调用组件的初始化方法
        if (typeof component.onAttached === 'function') {
            component.onAttached();
        }

        // 触发组件添加事件
        this.emit('componentAdded', {
            entity: this,
            component,
            componentType
        });

        console.log(`组件已添加到实体 ${this.id}: ${componentType}`);

        return this;
    }

    /**
     * 移除指定类型的组件
     * @param {string|Function} componentType - 组件类型名或组件类
     * @returns {boolean} 是否成功移除组件
     */
    removeComponent(componentType) {
        if (this.isDestroyed) {
            console.warn(`尝试从已销毁的实体移除组件: ${this.id}`);
            return false;
        }

        // 处理传入的是组件类的情况
        const typeName = typeof componentType === 'string'
            ? componentType
            : componentType.name;

        const component = this.components.get(typeName);
        if (!component) {
            console.warn(`实体 ${this.id} 不存在组件类型: ${typeName}`);
            return false;
        }

        // 调用组件的清理方法
        if (typeof component.onDetached === 'function') {
            component.onDetached();
        }

        // 清理组件的实体引用
        component.entity = null;

        // 从映射中移除
        this.components.delete(typeName);

        // 触发组件移除事件
        this.emit('componentRemoved', {
            entity: this,
            component,
            componentType: typeName
        });

        console.log(`组件已从实体 ${this.id} 移除: ${typeName}`);

        return true;
    }

    /**
     * 获取指定类型的组件
     * @param {string|Function} componentType - 组件类型名或组件类
     * @returns {Component|null} 组件实例，如果不存在则返回null
     */
    getComponent(componentType) {
        // 处理传入的是组件类的情况
        const typeName = typeof componentType === 'string'
            ? componentType
            : componentType.name;

        return this.components.get(typeName) || null;
    }

    /**
     * 检查是否拥有指定类型的组件
     * @param {string|Function} componentType - 组件类型名或组件类
     * @returns {boolean} 是否拥有该组件
     */
    hasComponent(componentType) {
        // 处理传入的是组件类的情况
        const typeName = typeof componentType === 'string'
            ? componentType
            : componentType.name;

        return this.components.has(typeName);
    }

    /**
     * 检查是否拥有所有指定类型的组件
     * @param {...(string|Function)} componentTypes - 组件类型名或组件类的列表
     * @returns {boolean} 是否拥有所有指定的组件
     */
    hasAllComponents(...componentTypes) {
        return componentTypes.every(type => this.hasComponent(type));
    }

    /**
     * 检查是否拥有任意一个指定类型的组件
     * @param {...(string|Function)} componentTypes - 组件类型名或组件类的列表
     * @returns {boolean} 是否拥有任意一个指定的组件
     */
    hasAnyComponent(...componentTypes) {
        return componentTypes.some(type => this.hasComponent(type));
    }

    /**
     * 获取所有组件
     * @returns {Array<Component>} 所有组件的数组
     */
    getAllComponents() {
        return Array.from(this.components.values());
    }

    /**
     * 获取所有组件类型名
     * @returns {Array<string>} 所有组件类型名的数组
     */
    getComponentTypes() {
        return Array.from(this.components.keys());
    }

    /**
     * 添加标签
     * @param {string} tag - 标签名
     * @returns {Entity} 返回自身，支持链式调用
     */
    addTag(tag) {
        this.tags.add(tag);
        return this;
    }

    /**
     * 移除标签
     * @param {string} tag - 标签名
     * @returns {boolean} 是否成功移除
     */
    removeTag(tag) {
        return this.tags.delete(tag);
    }

    /**
     * 检查是否拥有指定标签
     * @param {string} tag - 标签名
     * @returns {boolean} 是否拥有该标签
     */
    hasTag(tag) {
        return this.tags.has(tag);
    }

    /**
     * 获取所有标签
     * @returns {Array<string>} 所有标签的数组
     */
    getTags() {
        return Array.from(this.tags);
    }

    /**
     * 设置元数据
     * @param {string} key - 键
     * @param {*} value - 值
     * @returns {Entity} 返回自身，支持链式调用
     */
    setMetadata(key, value) {
        this.metadata.set(key, value);
        return this;
    }

    /**
     * 获取元数据
     * @param {string} key - 键
     * @returns {*} 元数据值
     */
    getMetadata(key) {
        return this.metadata.get(key);
    }

    /**
     * 设置实体的激活状态
     * @param {boolean} active - 是否激活
     */
    setActive(active) {
        if (this.isDestroyed) {
            console.warn(`尝试设置已销毁实体的激活状态: ${this.id}`);
            return;
        }

        if (this.isActive !== active) {
            this.isActive = active;

            // 通知所有组件状态变化
            this.components.forEach(component => {
                if (typeof component.onActiveChanged === 'function') {
                    component.onActiveChanged(active);
                }
            });

            // 触发状态变化事件
            this.emit('activeChanged', {
                entity: this,
                isActive: active
            });

            console.log(`实体 ${this.id} 激活状态已变更: ${active}`);
        }
    }

    /**
     * 销毁实体
     * 清理所有组件和引用
     */
    destroy() {
        if (this.isDestroyed) {
            console.warn(`实体已被销毁: ${this.id}`);
            return;
        }

        console.log(`开始销毁实体: ${this.id}`);

        // 标记为已销毁
        this.isDestroyed = true;
        this.isActive = false;

        // 触发销毁开始事件
        this.emit('beforeDestroy', { entity: this });

        // 清理所有组件
        const componentTypes = Array.from(this.components.keys());
        componentTypes.forEach(type => {
            const component = this.components.get(type);
            if (component) {
                // 直接清理组件，避免在销毁状态下调用removeComponent
                component.cleanup();
                component.entity = null;
                this.components.delete(type);

                // 触发组件移除事件
                this.emit('componentRemoved', {
                    entity: this,
                    component,
                    componentType: type
                });
            }
        });

        // 清理父子关系
        if (this.parent) {
            this.parent.removeChild(this);
        }

        // 清理所有子实体
        this.children.forEach(child => {
            child.destroy();
        });
        this.children.clear();

        // 清理其他数据
        this.tags.clear();
        this.metadata.clear();

        // 触发销毁完成事件
        this.emit('destroyed', { entity: this });

        // 清理事件监听器
        this.removeAllListeners();

        console.log(`实体销毁完成: ${this.id}`);
    }

    /**
     * 添加子实体
     * @param {Entity} child - 子实体
     */
    addChild(child) {
        if (child.parent) {
            child.parent.removeChild(child);
        }

        child.parent = this;
        this.children.add(child);

        this.emit('childAdded', { parent: this, child });
    }

    /**
     * 移除子实体
     * @param {Entity} child - 子实体
     */
    removeChild(child) {
        if (this.children.has(child)) {
            child.parent = null;
            this.children.delete(child);

            this.emit('childRemoved', { parent: this, child });
        }
    }

    /**
     * 获取实体的调试信息
     * @returns {Object} 调试信息对象
     */
    getDebugInfo() {
        return {
            id: this.id,
            name: this.name,
            isActive: this.isActive,
            isDestroyed: this.isDestroyed,
            createdAt: this.createdAt,
            componentCount: this.components.size,
            componentTypes: this.getComponentTypes(),
            tags: this.getTags(),
            childrenCount: this.children.size,
            hasParent: !!this.parent
        };
    }

    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `Entity(${this.id}, ${this.name}, components: ${this.components.size})`;
    }
}

export default Entity;
