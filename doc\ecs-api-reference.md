# ECS API参考文档

## 目录

- [核心类](#核心类)
  - [Entity](#entity)
  - [Component](#component)
  - [System](#system)
  - [ECSManager](#ecsmanager)
- [预定义组件](#预定义组件)
- [核心系统](#核心系统)
- [工具类](#工具类)
- [常量和枚举](#常量和枚举)

## 核心类

### Entity

实体类，游戏世界中的基本对象。

#### 构造函数

```javascript
new Entity(id, name = '')
```

**参数：**
- `id` (string): 实体的唯一标识符
- `name` (string, 可选): 实体的可读名称

#### 属性

- `id` (string): 实体ID
- `name` (string): 实体名称
- `isActive` (boolean): 是否激活
- `isDestroyed` (boolean): 是否已销毁
- `createdAt` (number): 创建时间戳
- `parent` (Entity): 父实体
- `children` (Set<Entity>): 子实体集合

#### 方法

##### addComponent(component)

添加组件到实体。

**参数：**
- `component` (Component): 要添加的组件实例

**返回值：** Entity - 返回自身，支持链式调用

**示例：**
```javascript
const transform = new TransformComponent(entity);
entity.addComponent(transform);
```

##### removeComponent(componentType)

移除指定类型的组件。

**参数：**
- `componentType` (string|Function): 组件类型名或组件类

**返回值：** boolean - 是否成功移除

##### getComponent(componentType)

获取指定类型的组件。

**参数：**
- `componentType` (string|Function): 组件类型名或组件类

**返回值：** Component|null - 组件实例或null

##### hasComponent(componentType)

检查是否拥有指定类型的组件。

**参数：**
- `componentType` (string|Function): 组件类型名或组件类

**返回值：** boolean - 是否拥有该组件

##### hasAllComponents(...componentTypes)

检查是否拥有所有指定类型的组件。

**参数：**
- `...componentTypes` (string|Function): 组件类型列表

**返回值：** boolean - 是否拥有所有指定组件

##### addTag(tag)

添加标签。

**参数：**
- `tag` (string): 标签名

**返回值：** Entity - 返回自身，支持链式调用

##### hasTag(tag)

检查是否拥有指定标签。

**参数：**
- `tag` (string): 标签名

**返回值：** boolean - 是否拥有该标签

##### setMetadata(key, value)

设置元数据。

**参数：**
- `key` (string): 键
- `value` (*): 值

**返回值：** Entity - 返回自身，支持链式调用

##### setActive(active)

设置实体的激活状态。

**参数：**
- `active` (boolean): 是否激活

##### destroy()

销毁实体，清理所有组件和引用。

#### 事件

- `componentAdded`: 组件被添加时触发
- `componentRemoved`: 组件被移除时触发
- `activeChanged`: 激活状态改变时触发
- `destroyed`: 实体被销毁时触发

### Component

组件基类，所有组件都应该继承自这个类。

#### 构造函数

```javascript
new Component(entity = null, options = {})
```

**参数：**
- `entity` (Entity): 所属的实体实例
- `options` (Object): 组件初始化选项

#### 属性

- `entity` (Entity): 所属实体
- `enabled` (boolean): 是否启用
- `isInitialized` (boolean): 是否已初始化
- `isDestroyed` (boolean): 是否已销毁
- `dependencies` (Array<string>): 依赖的组件类型列表

#### 生命周期方法

##### onAttached()

组件被附加到实体时调用。子类可以重写此方法。

##### onDetached()

组件从实体分离时调用。子类可以重写此方法。

##### onInitialize()

组件初始化时调用。子类应该重写此方法。

##### onUpdate(deltaTime)

组件更新时调用。子类可以重写此方法。

**参数：**
- `deltaTime` (number): 距离上一帧的时间间隔（秒）

##### onCleanup()

组件清理时调用。子类可以重写此方法。

#### 其他方法

##### setEnabled(enabled)

设置组件的启用状态。

**参数：**
- `enabled` (boolean): 是否启用

##### serialize()

序列化组件数据。

**返回值：** Object - 序列化后的数据

##### deserialize(data)

反序列化组件数据。

**参数：**
- `data` (Object): 序列化的数据

### System

系统基类，所有系统都应该继承自这个类。

#### 构造函数

```javascript
new System(scene, options = {})
```

**参数：**
- `scene` (BABYLON.Scene): Babylon.js场景实例
- `options` (Object): 系统初始化选项

#### 属性

- `scene` (BABYLON.Scene): Babylon.js场景引用
- `enabled` (boolean): 是否启用
- `priority` (number): 系统优先级
- `entities` (Set<Entity>): 系统关注的实体集合
- `requiredComponents` (Array<string>): 系统需要的组件类型列表
- `excludedComponents` (Array<string>): 系统排除的组件类型列表

#### 生命周期方法

##### onInitialize()

系统初始化时调用。子类应该重写此方法。

##### onUpdate(deltaTime)

系统更新时调用。子类应该重写此方法。

**参数：**
- `deltaTime` (number): 距离上一帧的时间间隔（秒）

##### updateEntity(entity, deltaTime)

更新单个实体。子类可以重写此方法。

**参数：**
- `entity` (Entity): 要更新的实体
- `deltaTime` (number): 时间间隔

##### onDispose()

系统清理时调用。子类可以重写此方法。

#### 其他方法

##### matchesEntity(entity)

检查实体是否符合系统的处理条件。

**参数：**
- `entity` (Entity): 要检查的实体

**返回值：** boolean - 是否符合条件

##### addEntity(entity)

手动添加实体到系统。

**参数：**
- `entity` (Entity): 要添加的实体

##### removeEntity(entity)

手动从系统移除实体。

**参数：**
- `entity` (Entity): 要移除的实体

##### setEnabled(enabled)

设置系统的启用状态。

**参数：**
- `enabled` (boolean): 是否启用

### ECSManager

ECS管理器，协调所有实体、组件和系统的运行。

#### 构造函数

```javascript
new ECSManager(scene, options = {})
```

**参数：**
- `scene` (BABYLON.Scene): Babylon.js场景实例
- `options` (Object): 管理器初始化选项

#### 生命周期方法

##### initialize()

初始化ECS管理器。

##### start()

启动ECS管理器。

##### stop()

停止ECS管理器。

##### pause()

暂停ECS管理器。

##### resume()

恢复ECS管理器。

##### update()

更新ECS管理器，应该在每帧被调用。

#### 实体管理

##### createEntity(name = '')

创建新实体。

**参数：**
- `name` (string): 实体名称

**返回值：** Entity - 新创建的实体

##### getEntity(entityId)

获取实体。

**参数：**
- `entityId` (string): 实体ID

**返回值：** Entity|null - 实体实例

##### removeEntity(entityId)

移除实体。

**参数：**
- `entityId` (string): 实体ID

**返回值：** boolean - 是否成功移除

##### getAllEntities()

获取所有实体。

**返回值：** Array<Entity> - 所有实体的数组

##### queryEntities(...componentTypes)

根据组件类型查询实体。

**参数：**
- `...componentTypes` (string|Function): 组件类型列表

**返回值：** Array<Entity> - 匹配的实体数组

#### 系统管理

##### addSystem(system, name = null)

添加系统。

**参数：**
- `system` (System): 系统实例
- `name` (string, 可选): 系统名称

##### getSystem(systemName)

获取系统。

**参数：**
- `systemName` (string): 系统名称

**返回值：** System|null - 系统实例

##### removeSystem(systemName)

移除系统。

**参数：**
- `systemName` (string): 系统名称

**返回值：** boolean - 是否成功移除

#### 组件管理

##### registerComponent(ComponentClass, name = null)

注册组件类型。

**参数：**
- `ComponentClass` (Function): 组件类
- `name` (string, 可选): 组件名称

##### getComponentType(componentName)

获取组件类型。

**参数：**
- `componentName` (string): 组件名称

**返回值：** Function|null - 组件类

#### 性能监控

##### getPerformanceStats()

获取性能统计信息。

**返回值：** Object - 性能统计数据

##### resetPerformanceStats()

重置性能统计。

#### 清理

##### cleanup()

清理ECS管理器，释放所有资源。

## 预定义组件

### TransformComponent

变换组件，管理实体的位置、旋转和缩放。

#### 主要方法

```javascript
setPosition(x, y, z)
setRotation(quaternion)
setScale(x, y, z)
translate(x, y, z)
rotate(x, y, z)
getWorldMatrix()
lookAt(target)
```

### RenderComponent

渲染组件，管理实体的视觉表现。

#### 主要方法

```javascript
setVisible(visible)
setColor(colorType, color)
setTexture(textureType, textureUrl)
getBoundingInfo()
```

### PhysicsComponent

物理组件，管理实体的物理属性。

#### 主要方法

```javascript
applyForce(force, point)
applyImpulse(impulse, point)
setLinearVelocity(velocity)
setAngularVelocity(angularVelocity)
```

### AnimationComponent

动画组件，管理实体的动画播放。

#### 主要方法

```javascript
play(name, options)
stop(name)
pause(name)
createTransformAnimation(name, property, keyframes, options)
```

### NetworkComponent

网络组件，管理实体的网络同步。

#### 主要方法

```javascript
setCustomData(key, value)
setNetworkOwnership(isOwned, ownerId)
```

## 核心系统

### RenderSystem

渲染系统，处理所有渲染相关的逻辑。

### PhysicsSystem

物理系统，处理物理模拟和碰撞检测。

## 工具类

### ComponentFactory

组件工厂，提供便捷的组件创建方法。

#### 主要方法

```javascript
createComponent(componentType, entity, options)
createFromPreset(presetName, entity, overrides)
createPlayerComponents(entity, options)
```

### EntityBuilder

实体构建器，提供流畅的实体创建API。

#### 主要方法

```javascript
create(name)
fromPreset(presetName, name)
at(x, y, z)
withRender(options)
withPhysics(options)
build()
```

### ComponentRegistry

组件注册表，管理所有组件类型的注册和元数据。

#### 主要方法

```javascript
register(ComponentClass, metadata, name)
createComponent(componentName, entity, options)
isRegistered(componentName)
```

## 常量和枚举

### ENTITY_PRESETS

实体预设常量：

```javascript
ENTITY_PRESETS.BASIC      // 基础实体
ENTITY_PRESETS.VISIBLE    // 可见实体
ENTITY_PRESETS.PHYSICS    // 物理实体
ENTITY_PRESETS.PLAYER     // 玩家实体
ENTITY_PRESETS.NPC        // NPC实体
ENTITY_PRESETS.STATIC     // 静态物体
```

### COMPONENT_TYPES

组件类型常量：

```javascript
COMPONENT_TYPES.TRANSFORM   // TransformComponent
COMPONENT_TYPES.RENDER      // RenderComponent
COMPONENT_TYPES.PHYSICS     // PhysicsComponent
COMPONENT_TYPES.ANIMATION   // AnimationComponent
COMPONENT_TYPES.NETWORK     // NetworkComponent
```

### MESH_TYPES

网格类型常量：

```javascript
MESH_TYPES.BOX       // 立方体
MESH_TYPES.SPHERE    // 球体
MESH_TYPES.CYLINDER  // 圆柱体
MESH_TYPES.PLANE     // 平面
MESH_TYPES.CAPSULE   // 胶囊体
```

## 使用示例

### 创建基本实体

```javascript
import { setupECS, ENTITY_PRESETS } from './src/ecs/index.js';

// 设置ECS系统
const ecs = setupECS(scene);
ecs.start();

// 创建玩家实体
const player = ecs.createEntity('player')
    .fromPreset(ENTITY_PRESETS.PLAYER)
    .at(0, 1, 0)
    .withColor('#ff0000')
    .build();

// 更新循环
function gameLoop() {
    ecs.update();
    requestAnimationFrame(gameLoop);
}
gameLoop();
```

### 自定义组件

```javascript
import { Component } from './src/ecs/index.js';

class HealthComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.maxHealth = options.maxHealth || 100;
        this.currentHealth = this.maxHealth;
    }
    
    takeDamage(amount) {
        this.currentHealth = Math.max(0, this.currentHealth - amount);
        this.emit('healthChanged', { 
            current: this.currentHealth, 
            max: this.maxHealth 
        });
    }
}
```

### 自定义系统

```javascript
import { System } from './src/ecs/index.js';

class HealthSystem extends System {
    constructor(scene, options = {}) {
        super(scene, options);
        this.requiredComponents = ['HealthComponent'];
    }
    
    updateEntity(entity, deltaTime) {
        const health = entity.getComponent('HealthComponent');
        if (health.currentHealth <= 0) {
            entity.destroy();
        }
    }
}
```
