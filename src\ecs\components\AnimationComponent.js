// src/ecs/components/AnimationComponent.js
// 动画组件 - 管理实体的动画播放和控制

import { 
    Animation,
    AnimationGroup,
    EasingFunction,
    CircleEase,
    BackEase,
    BounceEase
} from '@babylonjs/core';
import Component from '../Component.js';

/**
 * 动画组件类
 * 管理实体的动画播放、混合、过渡等功能
 * 支持骨骼动画、变换动画、材质动画等
 */
export class AnimationComponent extends Component {
    /**
     * 构造函数
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     */
    constructor(entity = null, options = {}) {
        super(entity, options);
        
        // 动画存储
        this.animations = new Map();           // name -> Animation
        this.animationGroups = new Map();      // name -> AnimationGroup
        this.currentAnimations = new Map();    // name -> playback info
        
        // 动画配置
        this.animationConfig = {
            defaultSpeed: options.defaultSpeed || 1.0,
            defaultLoop: options.defaultLoop || false,
            blendingSpeed: options.blendingSpeed || 0.1,
            enableBlending: options.enableBlending !== false,
            autoPlay: options.autoPlay || null,  // 自动播放的动画名称
            priority: options.priority || 0      // 动画优先级
        };
        
        // 动画状态
        this.isPlaying = false;
        this.isPaused = false;
        this.currentAnimation = null;
        this.nextAnimation = null;
        this.blendWeight = 0;
        
        // 动画队列
        this.animationQueue = [];
        this.queueMode = 'replace'; // 'replace', 'queue', 'blend'
        
        // 事件系统
        this.animationEvents = new Map(); // frame -> callback
        
        // 性能统计
        this.animationStats = {
            totalAnimations: 0,
            playingAnimations: 0,
            totalPlayTime: 0,
            averageFrameRate: 0,
            lastUpdateTime: 0
        };
        
        console.log(`动画组件已创建: ${this.entity?.id}`);
    }
    
    /**
     * 组件初始化
     */
    onInitialize() {
        // 如果设置了自动播放动画，开始播放
        if (this.animationConfig.autoPlay) {
            this.play(this.animationConfig.autoPlay);
        }
        
        // 触发动画组件就绪事件
        this.emit('animationReady', { component: this });
    }
    
    /**
     * 创建变换动画
     * @param {string} name - 动画名称
     * @param {string} property - 属性名 ('position', 'rotation', 'scaling')
     * @param {Array} keyframes - 关键帧数组 [{frame, value}, ...]
     * @param {Object} [options={}] - 动画选项
     */
    createTransformAnimation(name, property, keyframes, options = {}) {
        if (!this.entity?.scene) {
            console.error('无法创建动画：缺少场景引用');
            return null;
        }
        
        const scene = this.entity.scene;
        const transformComponent = this.getDependency('TransformComponent');
        
        if (!transformComponent) {
            console.warn('创建变换动画需要TransformComponent');
            return null;
        }
        
        // 创建动画对象
        const animation = new Animation(
            name,
            property,
            options.frameRate || 30,
            this.getAnimationType(property),
            options.loopMode || Animation.ANIMATIONLOOPMODE_CYCLE
        );
        
        // 设置关键帧
        const keys = keyframes.map(kf => ({
            frame: kf.frame,
            value: kf.value
        }));
        
        animation.setKeys(keys);
        
        // 设置缓动函数
        if (options.easingFunction) {
            animation.setEasingFunction(this.createEasingFunction(options.easingFunction));
        }
        
        // 存储动画
        this.animations.set(name, {
            animation,
            target: transformComponent,
            options,
            type: 'transform'
        });
        
        this.animationStats.totalAnimations++;
        
        console.log(`变换动画已创建: ${name}`);
        
        return animation;
    }
    
    /**
     * 创建材质动画
     * @param {string} name - 动画名称
     * @param {string} property - 材质属性名
     * @param {Array} keyframes - 关键帧数组
     * @param {Object} [options={}] - 动画选项
     */
    createMaterialAnimation(name, property, keyframes, options = {}) {
        const renderComponent = this.entity?.getComponent('RenderComponent');
        
        if (!renderComponent || !renderComponent.material) {
            console.warn('创建材质动画需要RenderComponent和材质');
            return null;
        }
        
        const animation = new Animation(
            name,
            property,
            options.frameRate || 30,
            this.getAnimationType(property),
            options.loopMode || Animation.ANIMATIONLOOPMODE_CYCLE
        );
        
        const keys = keyframes.map(kf => ({
            frame: kf.frame,
            value: kf.value
        }));
        
        animation.setKeys(keys);
        
        if (options.easingFunction) {
            animation.setEasingFunction(this.createEasingFunction(options.easingFunction));
        }
        
        this.animations.set(name, {
            animation,
            target: renderComponent.material,
            options,
            type: 'material'
        });
        
        this.animationStats.totalAnimations++;
        
        console.log(`材质动画已创建: ${name}`);
        
        return animation;
    }
    
    /**
     * 创建动画组
     * @param {string} name - 动画组名称
     * @param {Array<string>} animationNames - 动画名称数组
     * @param {Object} [options={}] - 选项
     */
    createAnimationGroup(name, animationNames, options = {}) {
        if (!this.entity?.scene) {
            console.error('无法创建动画组：缺少场景引用');
            return null;
        }
        
        const scene = this.entity.scene;
        const animationGroup = new AnimationGroup(name, scene);
        
        // 添加动画到组
        animationNames.forEach(animName => {
            const animData = this.animations.get(animName);
            if (animData) {
                animationGroup.addTargetedAnimation(animData.animation, animData.target);
            } else {
                console.warn(`动画不存在: ${animName}`);
            }
        });
        
        // 设置组属性
        animationGroup.loopAnimation = options.loop || false;
        animationGroup.speedRatio = options.speed || 1.0;
        
        this.animationGroups.set(name, {
            group: animationGroup,
            options,
            animations: animationNames
        });
        
        console.log(`动画组已创建: ${name}`);
        
        return animationGroup;
    }
    
    /**
     * 播放动画
     * @param {string} name - 动画或动画组名称
     * @param {Object} [options={}] - 播放选项
     */
    play(name, options = {}) {
        // 检查是否为动画组
        if (this.animationGroups.has(name)) {
            return this.playAnimationGroup(name, options);
        }
        
        // 检查是否为单个动画
        if (this.animations.has(name)) {
            return this.playAnimation(name, options);
        }
        
        console.warn(`动画不存在: ${name}`);
        return null;
    }
    
    /**
     * 播放单个动画
     * @private
     * @param {string} name - 动画名称
     * @param {Object} options - 播放选项
     */
    playAnimation(name, options = {}) {
        const animData = this.animations.get(name);
        if (!animData) return null;
        
        const scene = this.entity.scene;
        const { animation, target } = animData;
        
        // 停止当前动画（如果需要）
        if (options.stopCurrent !== false && this.currentAnimation) {
            this.stop(this.currentAnimation);
        }
        
        // 播放动画
        const animatable = scene.beginAnimation(
            target,
            options.from || 0,
            options.to || animation.getKeys().slice(-1)[0].frame,
            options.loop !== undefined ? options.loop : this.animationConfig.defaultLoop,
            options.speed || this.animationConfig.defaultSpeed,
            options.onAnimationEnd || null
        );
        
        // 记录播放信息
        this.currentAnimations.set(name, {
            animatable,
            startTime: performance.now(),
            options
        });
        
        this.currentAnimation = name;
        this.isPlaying = true;
        this.isPaused = false;
        
        this.animationStats.playingAnimations++;
        
        // 设置动画事件
        this.setupAnimationEvents(name, animatable);
        
        this.emit('animationStarted', { component: this, name, options });
        
        console.log(`动画开始播放: ${name}`);
        
        return animatable;
    }
    
    /**
     * 播放动画组
     * @private
     * @param {string} name - 动画组名称
     * @param {Object} options - 播放选项
     */
    playAnimationGroup(name, options = {}) {
        const groupData = this.animationGroups.get(name);
        if (!groupData) return null;
        
        const { group } = groupData;
        
        // 停止当前动画（如果需要）
        if (options.stopCurrent !== false && this.currentAnimation) {
            this.stop(this.currentAnimation);
        }
        
        // 设置播放参数
        if (options.speed !== undefined) {
            group.speedRatio = options.speed;
        }
        
        if (options.loop !== undefined) {
            group.loopAnimation = options.loop;
        }
        
        // 播放动画组
        group.start(
            options.loop !== undefined ? options.loop : group.loopAnimation,
            options.speed || group.speedRatio,
            options.from,
            options.to,
            options.onAnimationEnd
        );
        
        // 记录播放信息
        this.currentAnimations.set(name, {
            group,
            startTime: performance.now(),
            options
        });
        
        this.currentAnimation = name;
        this.isPlaying = true;
        this.isPaused = false;
        
        this.animationStats.playingAnimations++;
        
        this.emit('animationStarted', { component: this, name, options });
        
        console.log(`动画组开始播放: ${name}`);
        
        return group;
    }
    
    /**
     * 停止动画
     * @param {string} [name] - 动画名称，不指定则停止所有动画
     */
    stop(name = null) {
        if (name) {
            // 停止指定动画
            const playbackInfo = this.currentAnimations.get(name);
            if (playbackInfo) {
                if (playbackInfo.animatable) {
                    playbackInfo.animatable.stop();
                } else if (playbackInfo.group) {
                    playbackInfo.group.stop();
                }
                
                this.currentAnimations.delete(name);
                this.animationStats.playingAnimations--;
                
                if (this.currentAnimation === name) {
                    this.currentAnimation = null;
                    this.isPlaying = this.currentAnimations.size > 0;
                }
                
                this.emit('animationStopped', { component: this, name });
                
                console.log(`动画已停止: ${name}`);
            }
        } else {
            // 停止所有动画
            this.currentAnimations.forEach((playbackInfo, animName) => {
                if (playbackInfo.animatable) {
                    playbackInfo.animatable.stop();
                } else if (playbackInfo.group) {
                    playbackInfo.group.stop();
                }
                
                this.emit('animationStopped', { component: this, name: animName });
            });
            
            this.currentAnimations.clear();
            this.currentAnimation = null;
            this.isPlaying = false;
            this.animationStats.playingAnimations = 0;
            
            console.log('所有动画已停止');
        }
    }
    
    /**
     * 暂停动画
     * @param {string} [name] - 动画名称，不指定则暂停所有动画
     */
    pause(name = null) {
        if (name) {
            const playbackInfo = this.currentAnimations.get(name);
            if (playbackInfo) {
                if (playbackInfo.animatable) {
                    playbackInfo.animatable.pause();
                } else if (playbackInfo.group) {
                    playbackInfo.group.pause();
                }
                
                this.emit('animationPaused', { component: this, name });
            }
        } else {
            this.currentAnimations.forEach((playbackInfo, animName) => {
                if (playbackInfo.animatable) {
                    playbackInfo.animatable.pause();
                } else if (playbackInfo.group) {
                    playbackInfo.group.pause();
                }
                
                this.emit('animationPaused', { component: this, name: animName });
            });
            
            this.isPaused = true;
        }
    }
    
    /**
     * 恢复动画
     * @param {string} [name] - 动画名称，不指定则恢复所有动画
     */
    resume(name = null) {
        if (name) {
            const playbackInfo = this.currentAnimations.get(name);
            if (playbackInfo) {
                if (playbackInfo.animatable) {
                    playbackInfo.animatable.restart();
                } else if (playbackInfo.group) {
                    playbackInfo.group.restart();
                }
                
                this.emit('animationResumed', { component: this, name });
            }
        } else {
            this.currentAnimations.forEach((playbackInfo, animName) => {
                if (playbackInfo.animatable) {
                    playbackInfo.animatable.restart();
                } else if (playbackInfo.group) {
                    playbackInfo.group.restart();
                }
                
                this.emit('animationResumed', { component: this, name: animName });
            });
            
            this.isPaused = false;
        }
    }
    
    /**
     * 设置动画速度
     * @param {string} name - 动画名称
     * @param {number} speed - 播放速度
     */
    setSpeed(name, speed) {
        const playbackInfo = this.currentAnimations.get(name);
        if (playbackInfo) {
            if (playbackInfo.animatable) {
                playbackInfo.animatable.speedRatio = speed;
            } else if (playbackInfo.group) {
                playbackInfo.group.speedRatio = speed;
            }
            
            this.emit('animationSpeedChanged', { component: this, name, speed });
        }
    }
    
    /**
     * 获取动画类型
     * @private
     * @param {string} property - 属性名
     * @returns {number} 动画类型
     */
    getAnimationType(property) {
        switch (property) {
            case 'position':
            case 'scaling':
                return Animation.ANIMATIONTYPE_VECTOR3;
            case 'rotation':
                return Animation.ANIMATIONTYPE_QUATERNION;
            case 'diffuseColor':
            case 'specularColor':
            case 'emissiveColor':
                return Animation.ANIMATIONTYPE_COLOR3;
            case 'alpha':
            case 'intensity':
                return Animation.ANIMATIONTYPE_FLOAT;
            default:
                return Animation.ANIMATIONTYPE_FLOAT;
        }
    }
    
    /**
     * 创建缓动函数
     * @private
     * @param {string} type - 缓动类型
     * @returns {EasingFunction} 缓动函数
     */
    createEasingFunction(type) {
        switch (type) {
            case 'circle':
                return new CircleEase();
            case 'back':
                return new BackEase();
            case 'bounce':
                return new BounceEase();
            default:
                return new CircleEase();
        }
    }
    
    /**
     * 设置动画事件
     * @private
     * @param {string} name - 动画名称
     * @param {BABYLON.Animatable} animatable - 动画对象
     */
    setupAnimationEvents(name, animatable) {
        // 动画结束事件
        animatable.onAnimationEnd = () => {
            this.currentAnimations.delete(name);
            this.animationStats.playingAnimations--;
            
            if (this.currentAnimation === name) {
                this.currentAnimation = null;
                this.isPlaying = this.currentAnimations.size > 0;
            }
            
            this.emit('animationEnded', { component: this, name });
            
            // 处理动画队列
            this.processAnimationQueue();
        };
        
        // 动画循环事件
        animatable.onAnimationLoop = () => {
            this.emit('animationLoop', { component: this, name });
        };
    }
    
    /**
     * 处理动画队列
     * @private
     */
    processAnimationQueue() {
        if (this.animationQueue.length > 0) {
            const nextAnim = this.animationQueue.shift();
            this.play(nextAnim.name, nextAnim.options);
        }
    }
    
    /**
     * 添加动画到队列
     * @param {string} name - 动画名称
     * @param {Object} [options={}] - 播放选项
     */
    queueAnimation(name, options = {}) {
        this.animationQueue.push({ name, options });
        
        // 如果当前没有播放动画，立即开始
        if (!this.isPlaying) {
            this.processAnimationQueue();
        }
    }
    
    /**
     * 清空动画队列
     */
    clearQueue() {
        this.animationQueue.length = 0;
    }
    
    /**
     * 组件更新
     * @param {number} deltaTime - 时间间隔
     */
    onUpdate(deltaTime) {
        // 更新统计信息
        this.animationStats.lastUpdateTime = performance.now();
        
        // 处理动画混合
        if (this.animationConfig.enableBlending && this.nextAnimation) {
            this.updateBlending(deltaTime);
        }
    }
    
    /**
     * 更新动画混合
     * @private
     * @param {number} deltaTime - 时间间隔
     */
    updateBlending(deltaTime) {
        this.blendWeight += deltaTime * this.animationConfig.blendingSpeed;
        
        if (this.blendWeight >= 1.0) {
            // 混合完成，切换到新动画
            this.blendWeight = 1.0;
            this.stop(this.currentAnimation);
            this.currentAnimation = this.nextAnimation;
            this.nextAnimation = null;
            this.blendWeight = 0;
        }
    }
    
    /**
     * 序列化组件数据
     * @returns {Object} 序列化数据
     */
    onSerialize() {
        return {
            animationConfig: this.animationConfig,
            currentAnimation: this.currentAnimation,
            isPlaying: this.isPlaying,
            isPaused: this.isPaused,
            animationQueue: this.animationQueue
        };
    }
    
    /**
     * 反序列化组件数据
     * @param {Object} data - 序列化数据
     */
    onDeserialize(data) {
        if (data.animationConfig) {
            this.animationConfig = { ...this.animationConfig, ...data.animationConfig };
        }
        
        if (data.animationQueue) {
            this.animationQueue = [...data.animationQueue];
        }
        
        // 恢复播放状态
        if (data.currentAnimation && data.isPlaying) {
            this.play(data.currentAnimation);
            
            if (data.isPaused) {
                this.pause(data.currentAnimation);
            }
        }
    }
    
    /**
     * 组件清理
     */
    onCleanup() {
        // 停止所有动画
        this.stop();
        
        // 清理动画组
        this.animationGroups.forEach((groupData) => {
            groupData.group.dispose();
        });
        this.animationGroups.clear();
        
        // 清理动画
        this.animations.clear();
        
        // 清空队列
        this.clearQueue();
        
        // 清理事件
        this.animationEvents.clear();
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            animationConfig: this.animationConfig,
            isPlaying: this.isPlaying,
            isPaused: this.isPaused,
            currentAnimation: this.currentAnimation,
            animationCount: this.animations.size,
            animationGroupCount: this.animationGroups.size,
            queueLength: this.animationQueue.length,
            animationStats: this.animationStats
        };
    }
}

export default AnimationComponent;
