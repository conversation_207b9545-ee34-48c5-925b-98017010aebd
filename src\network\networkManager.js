// src/network/networkManager.js

import { NetworkConfig } from "../config/network";
import { encode, decode, Writer } from "../conspack/index";
import { createIntegerEmitter } from "../utils";
import { MSG_CODE_PING, makeCode2Protocal } from "./protocal";


// 根据Actors模型定义的消息类型
export const MESSAGE_SEND_TYPE_SEND = 0; // 最基本的类型, 消息发送到服务器, 可能需要回复, 也可能不需要回复
export const MESSAGE_SEND_TYPE_ASK  = 1; // 询问类型, 需要回复
export const MESSAGE_SEND_TYPE_TELL = 2; // 告知类型, 不需要回复
export const MESSAGE_SEND_TYPE_RPC  = 3; // RPC类型, 具有消息编号, 需要回复

/**
 * NetworkManager类负责处理与游戏服务器的WebSocket通信.
 * 它提供连接管理、消息发送、消息接收和事件监听功能, 并支持自动重连.
 * 消息格式: [ code, payload, meta ], code为消息码, 整数; payload为消息负载，数据类型与具体消息有关; meta为元数据, js对象.
 * meta的作用类似于http的headers, 回复消息需要将meta原样返回，可以添加内容, 为方便客户端解构, 使用js对象类型, 在服务器上会解析成哈希表.
 */
class NetworkManager {
    #writer;
    /**
     * 构造函数, 初始化网络管理器.
     * @param {string} serverUrl - WebSocket 服务器的URL, 例如 "ws://localhost:8080/cantos_server"
     */
    constructor(serverUrl) {
        this.serverUrl = serverUrl;     // WebSocket 服务器的 URL
        this.socket = null;             // WebSocket 实例
        this.messageQueue = [];         // 消息队列, 用于在连接建立前暂存待发送的消息
        this.isConnected = false;       // 当前连接状态标识

        // 事件侦听器, 数组, 消息码对应数组脚标, 数组元素为一个对象, 包括code(等于数组较脚标), cb(Set类型, 回调函数集)和doc三个键, 允许为同一消息类型注册多个回调.
        this.eventListeners = makeCode2Protocal();

        this.rpcListeners = new Map();                 // rpc消息侦听器, Map类型, 键为消息序列号, 值为回调函数.
        this.serialEmitter = createIntegerEmitter(1);  // 消息序列号发射器.

        // 自动重连相关属性
        this.reconnectAttempts = 0;                                         // 当前重连尝试次数
        this.maxReconnectAttempts = NetworkConfig.maxReconnectAttempts;     // 最大重连尝试次数
        this.initialReconnectDelay = NetworkConfig.initialReconnectDelay;   // 初始重连延迟(毫秒)
        this.reconnectTimeoutId = null;                                     // 重连定时器的ID, 用于清除

        // 心跳机制相关属性
        this.heartbeatIntervalMs = NetworkConfig.heartbeatIntervalMs;           // PING消息发送间隔 (25秒)
        this.serverActivityTimeoutMs = NetworkConfig.serverActivityTimeoutMs;   // 服务器活动超时时间 (10秒) - 若此时间内未收到任何服务器消息则认为连接丢失, 这个值不能小于heartbeatIntervalMs, 否则会被心跳检测认为是服务器掉线.
        this.pingIntervalId = null;                                             // PING发送定时器ID
        this.serverActivityTimeoutId = null;                                    // 服务器活动超时定时器ID

        // conspack
        this.#writer = new Writer(NetworkConfig.writerBufferSize);   // 初始ArrayBuffer大小, 超出会自动扩容
    }

    /**
     * JS对象序列化成二进制.
     * @param {any} data Any javascript value will be enpacked and sent to the server
     * @returns
     */
    #encode(data) {
        return encode(this.#writer, data);
    }

    /**
     * 二进制数据反序列化成JS对象.
     * @param {ArrayBuffer} data Binary data sent by the server.
     * @returns
     */
    #decode(data) {
        return decode(data);
    }

    /**
     * 建立与服务器的 WebSocket 连接.
     * 如果已连接或正在连接, 则不执行任何操作.
     */
    connect() {
        // 如果 WebSocket 实例存在且处于开放或连接中状态, 则不重复连接
        if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
            console.warn("WebSocket已连接或正在连接中.");
            return;
        }

        console.log(`尝试连接到Cantos服务器: ${this.serverUrl}`);
        // 创建一个新的 WebSocket 实例
        this.socket = new WebSocket(this.serverUrl);
        this.socket.binaryType = "arraybuffer"; // https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/binaryType

        // --- WebSocket 事件处理 ---
        // 连接成功建立时触发
        this.socket.onopen = (event) => {
            this.isConnected = true;        // 更新连接状态
            this.reconnectAttempts = 0;     // 成功连接后重置重连尝试次数
            // 如果存在重连定时器, 清除它
            if (this.reconnectTimeoutId) {
                clearTimeout(this.reconnectTimeoutId);
                this.reconnectTimeoutId = null;
            }
            //this.startHeartbeat(); // 连接成功后启动心跳
            console.log("成功连接到Cantos服务器!");

            // 处理连接前堆积的消息队列, 循环直到队列为空或连接状态改变
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift(); // 取出并移除队列头部的消息
                // 再次检查连接状态, 以防在处理队列时连接意外断开
                if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                    this.socket.send(message); // 发送消息
                } else {
                    // 如果连接断开, 将未发送的消息重新放回队列头部
                    this.messageQueue.unshift(message);
                    console.warn("连接在处理消息队列时断开, 剩余消息将重新排队.");
                    break;
                }
            }
            console.log("消息队列已处理.");
        };

        // 收到服务器消息时触发
        this.socket.onmessage = (event) => {
            try {
                this.#resetServerActivityTimeout(); // 收到任何消息都重置服务器活动超时
                // 尝试将收到的数据解析为 JSON 对象
                console.log("原始服务器消息，", "类型：", typeof event.data, event.data);

                const parsedMessage = this.#decode(event.data);
                console.debug("收到服务器消息:", parsedMessage);

                const [ code, payload, meta ] = parsedMessage;
                const msgType = meta?.type;

                if(msgType === MESSAGE_SEND_TYPE_RPC) { // RPC 消息
                    const msgSerial = meta.sn; // 消息序列号
                    if (this.rpcListeners.has(msgSerial)) {
                        const callbacks = this.rpcListeners.get(msgSerial);
                        callbacks.forEach(callback => {
                            try {
                                callback(payload, meta); // 将 payload 作为参数传递给回调函数
                            } catch (cbError) {
                                console.error(`处理RPC消息序列号'${msgSerial}'的回调时出错:`, cbError);
                            }
                        });
                        this.rpcListeners.delete(msgSerial);
                    } else {
                        console.warn(`收到未注册的RPC消息序列号: ${msgSerial}`, parsedMessage);
                    }
                } else { // 非RPC消息
                    // 如果存在该消息类型的监听器, 则调用所有注册的回调函数
                    if (this.eventListeners[code]) {
                        // 遍历 Set 中的每个回调函数并执行
                        this.eventListeners[code].cb.forEach(callback => {
                            try {
                                callback(payload, meta); // 将 payload 作为参数传递给回调函数
                            } catch (cbError) {
                                console.error(`处理消息类型'${code}:${this.eventListeners[code].name}'的回调时出错:`, cbError);
                            }
                        });
                    } else {
                        console.warn(`收到未知消息类型: ${code}`, parsedMessage);
                    }
                }
            } catch (error) {
                console.error("解析服务器消息时出错:", error, "原始数据:", event.data);
            }
        };

        // 连接关闭时触发
        this.socket.onclose = (event) => {
            this.isConnected = false;   // 更新连接状态
            this.socket = null;         // 清除 WebSocket 实例
            this.#stopHeartbeat();      // 连接关闭时停止心跳
            console.warn(`与Cantos服务器连接已关闭, 代码: ${event.code}, 原因: ${event.reason}.`);

            // 尝试自动重连
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                // 采用简单的指数退避策略计算重连延迟
                // 例如: 1s, 2s, 4s, 8s, 16s, 30s(上限) ...
                const delay = Math.min(
                    this.initialReconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
                    30000 // 最大延迟30秒
                );
                console.log(`尝试在${delay / 1000}秒后重连, 第${this.reconnectAttempts}次尝试...`);
                // 设置定时器, 延迟后调用 connect 方法
                this.reconnectTimeoutId = setTimeout(() => this.connect(), delay);
            } else {
                console.error(`达到最大重连尝试次数(${this.maxReconnectAttempts}), 停止重连, 请检查网络或服务器状态.`);
            }
        };

        // 发生错误时触发
        this.socket.onerror = (error) => {
            console.error("WebSocket onError:", error);
            // this.#stopHeartbeat(); // 通常错误后会触发onclose, onclose中会停止心跳
            // 通常, 错误事件后会紧跟着一个 close 事件, 所以这里不需要额外的重连逻辑.
        };
    }

    /**
     * 发送消息到服务器.
     * 消息格式为 [ code: int, payload: any, meta: Object ].
     * 如果连接未建立或未打开, 消息会进入队列等待发送.
     * @param {number} msgCode - 消息码
     * @param {*} payload - 消息的有效载荷数据, 可以是任何可序列化的 JavaScript 对象
     * @param {Object} metaData - 元数据
     */
    send(msgCode, payload, metaData = {}) {
        // 在cl-conspack中会转换成alist
        const messageObject = [
            msgCode,
            payload,
            {
                ...metaData,
                clientTime: Date.now(),
            },
        ];
        const messageBin = this.#encode(messageObject); // 将消息对象转换为二进制数据

        // 检查连接是否已建立且处于打开状态
        if (this.isConnected && this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(messageBin);
        } else {
            // 如果连接未准备好, 将消息添加到队列中
            this.messageQueue.push(messageBin);
            console.warn(`Socket未就绪, 放入队列, 消息类型: SEND, 消息码: '${msgCode}', 队列当前长度: ${this.messageQueue.length}.`);
        }
    }

    /**
     * 发送一个RPC请求到服务器.
     * 通常一个RPC请求对应一个响应回调, 因此`eventListeners[msgCode].cb`中最多存在一个回调作为默认回调.
     * @param {number} msgCode - 消息码
     * @param {*} payload - 消息负载
     * @param {Function | null | undefined} callback - 收到消息后的回调函数, 如果为广义FALSE, 就使用`eventListeners[msgCode].cb`中的默认回调, 默认回调最多配置一个.
     * @param {Object} metaData - 元数据
     */
    rpcCall(msgCode, payload, callback, metaData = {}) {
        if (typeof msgCode !== 'number') {
            console.error(`RPC消息监听器失败: msgCode必须是整数, msgCode: ${msgCode}.`);
            return;
        }
        if (callback && typeof callback !== 'function') { // callback can be null/undefined (falsy), but if provided, must be a function
            console.error(`RPC消息监听器失败: callback必须是函数或广义FALSE, msgCode: ${msgCode}, callback: ${callback}.`);
            return;
        }

        let rpcCallbackToUse = null;
        // 注册RPC回调: 优先使用传入的callback, 否则使用配置的单个默认回调.
        if (callback) {
            rpcCallbackToUse = callback;
        } else {
            const savedCb = this.eventListeners[msgCode]?.cb;
            if (savedCb && savedCb.size === 1) {
                // Get the single callback from the Set
                rpcCallbackToUse = savedCb.values().next().value;
            } else {
                if(!savedCb || savedCb.size === 0) {
                    console.error(`RPC 调用失败: 消息码 '${msgCode}' 没有提供回调函数, 且未找到已注册的单个默认监听器.`);
                } else {
                    console.error(`RPC 调用失败: 消息码 '${msgCode}' 没有提供回调函数, 且存在 ${savedCb.size} 个已注册的默认监听器, 无法确定唯一回调.`);
                }
                return; // 无法确定回调, 不发送RPC请求
            }
        }

        const sn = this.serialEmitter(); // 生成消息序列号
        const meta = {
            ...metaData,
            sn: sn,
            type: MESSAGE_SEND_TYPE_RPC,
            clientTime: Date.now(),
        };
        this.send(msgCode, payload, meta);

        // 仅在成功确定回调后才注册监听器
        this.rpcListeners.set(sn, new Set([rpcCallbackToUse]));
    }

    /**
     * 注册一个回调函数, 用于监听特定类型的服务器消息.
     * @param {number} msgCode - 要监听的消息码.
     * @param {Function | null | undefined} callback - 消息到达时要执行的回调函数, 它将接收消息的meta和payload作为参数.
     *                              如果为函数, 就表示ASK类型的消息, 例如: `(data) => { console.log('收到数据:', data); }`
     *                              如果为广义FALSE, 则表示一种不需要返回值的TELL消息.
     * @param {string} name - 消息名称, 用于打印日志和调试等.
     * @param {string} doc - 对此消息协议的描述信息, 方便理解其用途.
     */
    on(msgCode, callback, name, doc) {
        if ((typeof callback !== 'function') || (!callback)) {
            console.error(`注册消息类型'${msgCode}'的监听器失败: callback必须是函数或者为广义FALSE.`);
            return;
        }

        const canonicalName = name || `MSG-${msgCode}`;
        // 如果该消息类型还没有对应的 Set, 则创建一个新的 Set
        if (!this.eventListeners[msgCode]) {
            this.eventListeners[msgCode] = {
                code: msgCode,
                name: canonicalName,
                cb: new Set(),
                doc: doc || "",
            };
        }
        // 将回调函数添加到对应消息类型的Set中, 允许定义没有回调的函数
        callback && this.eventListeners[msgCode].cb.add(callback);
        console.log(`已为消息'${msgCode}:${canonicalName}'注册监听器.`);
    }

    /**
     * 移除一个已注册的特定消息类型的回调函数.
     * @param {number} msgCode - 要移除监听器的消息码.
     * @param {Function} callback - 要移除的回调函数.
     */
    off(msgCode, callback) {
        if (!this.eventListeners[msgCode]) {
            console.warn(`消息码'${msgCode}'没有注册的监听器.`);
            return;
        }

        const messageProtocal = this.eventListeners[msgCode];
        // 从cb的Set数据结构中删除指定的回调函数
        if (messageProtocal.cb.delete(callback)) {
            console.log(`已为消息类型'${msgCode}'移除监听器. `);
        } else {
            console.warn(`消息类型'${msgCode}'不存在此回调函数, 无法移除.`);
        }
    }

    /**
     * 主动断开与服务器的 WebSocket 连接.
     * 这会清除任何正在进行的重连尝试.
     */
    disconnect() {
        if (this.socket) {
            // 清除任何正在进行的重连定时器, 防止断开后立即尝试重连
            if (this.reconnectTimeoutId) {
                clearTimeout(this.reconnectTimeoutId);
                this.reconnectTimeoutId = null;
            }
            this.#stopHeartbeat(); // 主动断开时停止心跳
            this.socket.close(); // 关闭 WebSocket 连接
            this.isConnected = false; // 更新连接状态
            this.socket = null; // 清空实例
            console.log("已主动断开与服务器的连接.");
        } else {
            console.warn("WebSocket未连接或已关闭, 无需断开.");
        }
    }


    // --- 心跳机制方法 ---

    /**
     * 启动心跳机制.
     * @private
     */
    startHeartbeat() {
        console.log("启动心跳机制, 先停止可能存在的旧心跳");
        this.#stopHeartbeat(); // 先停止可能存在的旧心跳
        console.log(`启动心跳机制: PING间隔 ${this.heartbeatIntervalMs / 1000}s, 服务器活动超时 ${this.serverActivityTimeoutMs / 1000}s.`);
        this.pingIntervalId = setInterval(() => {
            this.#sendPing();
        }, this.heartbeatIntervalMs);
        this.#resetServerActivityTimeout(); // 启动服务器活动监控
    }

    /**
     * 停止心跳机制.
     * @private
     */
    #stopHeartbeat() {
        clearInterval(this.pingIntervalId);
        clearTimeout(this.serverActivityTimeoutId);
        this.pingIntervalId = null;
        this.serverActivityTimeoutId = null;
        console.log("心跳机制已停止.");
    }

    /**
     * 发送PING消息到服务器.
     * @private
     */
    #sendPing() {
        if (this.isConnected && this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.debug("发送PING至服务器", Date.now());
            this.send(MSG_CODE_PING, "PING"); // PING消息的payload可以为空或简单字符串
        }
    }

    /**
     * 重置服务器活动超时计时器.
     * @private
     */
    #resetServerActivityTimeout() {
        clearTimeout(this.serverActivityTimeoutId);
        this.serverActivityTimeoutId = setTimeout(() => {
            console.warn(`服务器活动超时(${this.serverActivityTimeoutMs / 1000}s), 未收到服务器消息, 主动关闭连接.`);
            this.socket?.close(); // 主动关闭socket, 会触发onclose中的重连逻辑
        }, this.serverActivityTimeoutMs);
    }

}

// 导出 NetworkManager 的一个单例实例, 以便在应用程序中全局使用.
// 注意: 请根据你的实际服务器配置, 替换以下 URL.
// 示例: 如果你的 Lisp 服务器在本地运行, 端口为 8080, WebSocket 路径为 /cantos_server
//const cantosServerUrl = "ws://**************:5588/ws/cantos?r=TESTCONNTOKEN00";
const cantosServerUrl = `${NetworkConfig.serverAddress}${NetworkConfig.serverPath}?r=${NetworkConfig.connId}`
const networkManagerInstance = new NetworkManager(cantosServerUrl);

export default networkManagerInstance;
