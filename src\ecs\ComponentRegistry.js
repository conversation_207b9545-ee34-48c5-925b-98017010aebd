// src/ecs/ComponentRegistry.js
// 组件注册表 - 管理所有组件类型的注册和创建

import { EventEmitter } from '../utils/eventEmitter.js';

/**
 * 组件注册表类
 * 负责管理所有组件类型的注册、创建和元数据
 */
export class ComponentRegistry extends EventEmitter {
    constructor() {
        super();
        
        // 组件类型存储 - name -> ComponentClass
        this.componentTypes = new Map();
        
        // 组件元数据存储 - name -> metadata
        this.componentMetadata = new Map();
        
        // 组件依赖关系图
        this.dependencyGraph = new Map();
        
        // 组件分组
        this.componentGroups = new Map();
        
        console.log("组件注册表已创建");
    }
    
    /**
     * 注册组件类型
     * @param {Function} ComponentClass - 组件类
     * @param {Object} [metadata={}] - 组件元数据
     * @param {string} [name] - 组件名称，默认使用类名
     */
    register(ComponentClass, metadata = {}, name = null) {
        const componentName = name || ComponentClass.name;
        
        if (this.componentTypes.has(componentName)) {
            console.warn(`组件类型已注册: ${componentName}`);
            return false;
        }
        
        // 验证组件类
        if (!this.validateComponentClass(ComponentClass)) {
            console.error(`无效的组件类: ${componentName}`);
            return false;
        }
        
        // 注册组件类型
        this.componentTypes.set(componentName, ComponentClass);
        
        // 存储元数据
        const fullMetadata = {
            name: componentName,
            description: '',
            version: '1.0.0',
            dependencies: [],
            group: 'default',
            singleton: false,
            serializable: true,
            ...metadata
        };
        
        this.componentMetadata.set(componentName, fullMetadata);
        
        // 处理依赖关系
        if (fullMetadata.dependencies && fullMetadata.dependencies.length > 0) {
            this.dependencyGraph.set(componentName, fullMetadata.dependencies);
        }
        
        // 添加到分组
        this.addToGroup(fullMetadata.group, componentName);
        
        // 触发注册事件
        this.emit('componentRegistered', {
            name: componentName,
            componentClass: ComponentClass,
            metadata: fullMetadata
        });
        
        console.log(`组件类型已注册: ${componentName}`);
        
        return true;
    }
    
    /**
     * 批量注册组件
     * @param {Array} components - 组件配置数组
     */
    registerBatch(components) {
        const results = [];
        
        components.forEach(config => {
            const { componentClass, metadata, name } = config;
            const success = this.register(componentClass, metadata, name);
            results.push({ name: name || componentClass.name, success });
        });
        
        const successCount = results.filter(r => r.success).length;
        console.log(`批量注册完成: ${successCount}/${results.length} 个组件成功注册`);
        
        return results;
    }
    
    /**
     * 注销组件类型
     * @param {string} componentName - 组件名称
     * @returns {boolean} 是否成功注销
     */
    unregister(componentName) {
        if (!this.componentTypes.has(componentName)) {
            console.warn(`组件类型不存在: ${componentName}`);
            return false;
        }
        
        // 检查是否有其他组件依赖此组件
        const dependents = this.getDependents(componentName);
        if (dependents.length > 0) {
            console.error(`无法注销组件 ${componentName}，以下组件依赖它: ${dependents.join(', ')}`);
            return false;
        }
        
        // 获取组件信息
        const componentClass = this.componentTypes.get(componentName);
        const metadata = this.componentMetadata.get(componentName);
        
        // 从各种存储中移除
        this.componentTypes.delete(componentName);
        this.componentMetadata.delete(componentName);
        this.dependencyGraph.delete(componentName);
        
        // 从分组中移除
        if (metadata && metadata.group) {
            this.removeFromGroup(metadata.group, componentName);
        }
        
        // 触发注销事件
        this.emit('componentUnregistered', {
            name: componentName,
            componentClass,
            metadata
        });
        
        console.log(`组件类型已注销: ${componentName}`);
        
        return true;
    }
    
    /**
     * 获取组件类型
     * @param {string} componentName - 组件名称
     * @returns {Function|null} 组件类
     */
    getComponentType(componentName) {
        return this.componentTypes.get(componentName) || null;
    }
    
    /**
     * 获取组件元数据
     * @param {string} componentName - 组件名称
     * @returns {Object|null} 组件元数据
     */
    getComponentMetadata(componentName) {
        return this.componentMetadata.get(componentName) || null;
    }
    
    /**
     * 检查组件是否已注册
     * @param {string} componentName - 组件名称
     * @returns {boolean} 是否已注册
     */
    isRegistered(componentName) {
        return this.componentTypes.has(componentName);
    }
    
    /**
     * 创建组件实例
     * @param {string} componentName - 组件名称
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {Component|null} 组件实例
     */
    createComponent(componentName, entity, options = {}) {
        const ComponentClass = this.getComponentType(componentName);
        if (!ComponentClass) {
            console.error(`未注册的组件类型: ${componentName}`);
            return null;
        }
        
        const metadata = this.getComponentMetadata(componentName);
        
        // 检查单例限制
        if (metadata.singleton && entity.hasComponent(componentName)) {
            console.warn(`组件 ${componentName} 是单例组件，实体已存在该组件`);
            return null;
        }
        
        // 检查依赖组件
        if (metadata.dependencies && metadata.dependencies.length > 0) {
            const missingDeps = metadata.dependencies.filter(dep => !entity.hasComponent(dep));
            if (missingDeps.length > 0) {
                console.error(`组件 ${componentName} 缺少依赖组件: ${missingDeps.join(', ')}`);
                return null;
            }
        }
        
        try {
            // 创建组件实例
            const component = new ComponentClass(entity, options);
            
            // 触发创建事件
            this.emit('componentCreated', {
                name: componentName,
                component,
                entity,
                options
            });
            
            return component;
            
        } catch (error) {
            console.error(`创建组件失败 ${componentName}:`, error);
            return null;
        }
    }
    
    /**
     * 获取所有已注册的组件类型名称
     * @returns {Array<string>} 组件类型名称数组
     */
    getAllComponentNames() {
        return Array.from(this.componentTypes.keys());
    }
    
    /**
     * 获取指定分组的组件
     * @param {string} groupName - 分组名称
     * @returns {Array<string>} 组件名称数组
     */
    getComponentsByGroup(groupName) {
        return this.componentGroups.get(groupName) || [];
    }
    
    /**
     * 获取所有分组
     * @returns {Array<string>} 分组名称数组
     */
    getAllGroups() {
        return Array.from(this.componentGroups.keys());
    }
    
    /**
     * 获取组件的依赖组件
     * @param {string} componentName - 组件名称
     * @returns {Array<string>} 依赖组件名称数组
     */
    getDependencies(componentName) {
        return this.dependencyGraph.get(componentName) || [];
    }
    
    /**
     * 获取依赖指定组件的组件列表
     * @param {string} componentName - 组件名称
     * @returns {Array<string>} 依赖者组件名称数组
     */
    getDependents(componentName) {
        const dependents = [];
        
        this.dependencyGraph.forEach((deps, name) => {
            if (deps.includes(componentName)) {
                dependents.push(name);
            }
        });
        
        return dependents;
    }
    
    /**
     * 验证组件依赖关系
     * @param {string} componentName - 组件名称
     * @returns {Object} 验证结果
     */
    validateDependencies(componentName) {
        const dependencies = this.getDependencies(componentName);
        const result = {
            valid: true,
            missingDependencies: [],
            circularDependencies: []
        };
        
        // 检查缺失的依赖
        dependencies.forEach(dep => {
            if (!this.isRegistered(dep)) {
                result.missingDependencies.push(dep);
                result.valid = false;
            }
        });
        
        // 检查循环依赖
        const visited = new Set();
        const recursionStack = new Set();
        
        const hasCircularDependency = (name) => {
            if (recursionStack.has(name)) {
                result.circularDependencies.push(name);
                result.valid = false;
                return true;
            }
            
            if (visited.has(name)) {
                return false;
            }
            
            visited.add(name);
            recursionStack.add(name);
            
            const deps = this.getDependencies(name);
            for (const dep of deps) {
                if (hasCircularDependency(dep)) {
                    return true;
                }
            }
            
            recursionStack.delete(name);
            return false;
        };
        
        hasCircularDependency(componentName);
        
        return result;
    }
    
    /**
     * 验证组件类
     * @private
     * @param {Function} ComponentClass - 组件类
     * @returns {boolean} 是否有效
     */
    validateComponentClass(ComponentClass) {
        // 检查是否是函数
        if (typeof ComponentClass !== 'function') {
            return false;
        }
        
        // 检查是否有必要的方法（通过原型检查）
        const prototype = ComponentClass.prototype;
        const requiredMethods = ['onAttached', 'onDetached', 'update'];
        
        // 注意：这里不强制要求所有方法都存在，因为基类已经提供了默认实现
        return true;
    }
    
    /**
     * 添加组件到分组
     * @private
     * @param {string} groupName - 分组名称
     * @param {string} componentName - 组件名称
     */
    addToGroup(groupName, componentName) {
        if (!this.componentGroups.has(groupName)) {
            this.componentGroups.set(groupName, []);
        }
        
        const group = this.componentGroups.get(groupName);
        if (!group.includes(componentName)) {
            group.push(componentName);
        }
    }
    
    /**
     * 从分组中移除组件
     * @private
     * @param {string} groupName - 分组名称
     * @param {string} componentName - 组件名称
     */
    removeFromGroup(groupName, componentName) {
        const group = this.componentGroups.get(groupName);
        if (group) {
            const index = group.indexOf(componentName);
            if (index !== -1) {
                group.splice(index, 1);
            }
            
            // 如果分组为空，删除分组
            if (group.length === 0) {
                this.componentGroups.delete(groupName);
            }
        }
    }
    
    /**
     * 导出组件注册信息
     * @returns {Object} 注册信息
     */
    exportRegistry() {
        const registry = {
            components: {},
            groups: Object.fromEntries(this.componentGroups),
            dependencies: Object.fromEntries(this.dependencyGraph)
        };
        
        this.componentMetadata.forEach((metadata, name) => {
            registry.components[name] = {
                ...metadata,
                className: this.componentTypes.get(name).name
            };
        });
        
        return registry;
    }
    
    /**
     * 清理注册表
     */
    clear() {
        console.log("清理组件注册表...");
        
        // 触发清理事件
        this.emit('registryClearing');
        
        // 清空所有存储
        this.componentTypes.clear();
        this.componentMetadata.clear();
        this.dependencyGraph.clear();
        this.componentGroups.clear();
        
        // 清理事件监听器
        this.removeAllListeners();
        
        console.log("组件注册表清理完成");
    }
    
    /**
     * 获取注册表统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalComponents: this.componentTypes.size,
            totalGroups: this.componentGroups.size,
            componentsWithDependencies: this.dependencyGraph.size,
            groupDistribution: Object.fromEntries(
                Array.from(this.componentGroups.entries()).map(([group, components]) => [
                    group,
                    components.length
                ])
            )
        };
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            stats: this.getStats(),
            componentNames: this.getAllComponentNames(),
            groups: this.getAllGroups(),
            registry: this.exportRegistry()
        };
    }
}

// 创建全局单例实例
export const componentRegistry = new ComponentRegistry();

export default componentRegistry;
