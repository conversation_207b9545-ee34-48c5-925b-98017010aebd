# ECS (Entity-Component-System) 架构

《Cantos》项目的实体组件系统实现，提供了一个强大、灵活且高性能的游戏对象管理框架。

## 🚀 特性

- **🏗️ 模块化架构**：清晰的实体、组件、系统分离
- **⚡ 高性能**：优化的更新循环和内存管理
- **🔧 易于扩展**：简单的组件和系统创建API
- **🎮 游戏就绪**：预定义的常用组件和系统
- **🌐 网络支持**：内置网络同步组件
- **🔍 调试友好**：完整的调试和性能监控工具
- **📱 TypeScript友好**：完整的中文注释和类型提示

## 📁 项目结构

```
src/ecs/
├── index.js                    # 主入口文件
├── Entity.js                   # 实体类
├── Component.js                # 组件基类
├── System.js                   # 系统基类
├── ECSManager.js               # ECS管理器
├── ComponentRegistry.js        # 组件注册表
├── components/                 # 预定义组件
│   ├── TransformComponent.js   # 变换组件
│   ├── RenderComponent.js      # 渲染组件
│   ├── PhysicsComponent.js     # 物理组件
│   ├── AnimationComponent.js   # 动画组件
│   └── NetworkComponent.js     # 网络组件
├── systems/                    # 核心系统
│   ├── RenderSystem.js         # 渲染系统
│   └── PhysicsSystem.js        # 物理系统
├── utils/                      # 工具类
│   ├── ComponentFactory.js     # 组件工厂
│   └── EntityBuilder.js        # 实体构建器
├── integration-example.js      # 集成示例
├── *.unit.test.js              # 单元测试
└── README.md                   # 本文件
```

## 🚀 快速开始

### 1. 基础设置

```javascript
import { setupECS } from './src/ecs/index.js';

// 设置ECS系统
const ecs = setupECS(scene, {
    manager: { maxEntities: 5000 },
    render: { enableLOD: true },
    physics: { gravity: { x: 0, y: -9.81, z: 0 } }
});

ecs.start();
```

### 2. 创建实体

```javascript
// 使用预设创建玩家
const player = ecs.createEntity('player')
    .fromPreset('player')
    .at(0, 1, 0)
    .withColor('#0066ff')
    .build();

// 手动创建实体
const enemy = ecs.createEntity('enemy')
    .withRender({ meshType: 'sphere' })
    .withPhysics({ mass: 1.0 })
    .withAnimation({ autoPlay: 'idle' })
    .build();
```

### 3. 游戏循环

```javascript
function gameLoop() {
    ecs.update();
    scene.render();
    requestAnimationFrame(gameLoop);
}
gameLoop();
```

## 🧩 核心概念

### 实体 (Entity)
游戏世界中的基本对象，本身不包含数据或逻辑，只是组件的容器。

```javascript
const entity = new Entity('player', 'Player Character');
entity.addComponent(new TransformComponent(entity));
entity.addTag('player');
entity.setMetadata('health', 100);
```

### 组件 (Component)
存储数据和状态的容器，定义了实体的属性。

```javascript
class HealthComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.maxHealth = options.maxHealth || 100;
        this.currentHealth = this.maxHealth;
    }
    
    takeDamage(amount) {
        this.currentHealth = Math.max(0, this.currentHealth - amount);
    }
}
```

### 系统 (System)
包含游戏逻辑，处理具有特定组件组合的实体。

```javascript
class HealthSystem extends System {
    constructor(scene, options = {}) {
        super(scene, options);
        this.requiredComponents = ['HealthComponent'];
    }
    
    updateEntity(entity, deltaTime) {
        const health = entity.getComponent('HealthComponent');
        if (health.currentHealth <= 0) {
            entity.destroy();
        }
    }
}
```

## 📦 预定义组件

### TransformComponent
管理实体的位置、旋转和缩放。

```javascript
const transform = entity.getComponent('TransformComponent');
transform.setPosition(10, 0, 5);
transform.rotate(0, Math.PI / 4, 0);
transform.lookAt(targetPosition);
```

### RenderComponent
管理实体的视觉表现。

```javascript
const render = entity.getComponent('RenderComponent');
render.setVisible(true);
render.setColor('diffuse', new Color3(1, 0, 0));
render.setTexture('diffuseTexture', 'path/to/texture.jpg');
```

### PhysicsComponent
管理实体的物理属性。

```javascript
const physics = entity.getComponent('PhysicsComponent');
physics.applyForce(new Vector3(0, 100, 0));
physics.setLinearVelocity(new Vector3(5, 0, 0));
```

### AnimationComponent
管理实体的动画播放。

```javascript
const animation = entity.getComponent('AnimationComponent');
animation.play('walk', { loop: true, speed: 1.5 });
animation.createTransformAnimation('bounce', 'position', keyframes);
```

### NetworkComponent
管理实体的网络同步。

```javascript
const network = entity.getComponent('NetworkComponent');
network.setCustomData('playerName', 'Alice');
network.setNetworkOwnership(true, 'player1');
```

## 🛠️ 工具类

### ComponentFactory
便捷的组件创建工具。

```javascript
import { componentFactory } from './src/ecs/index.js';

// 使用预设创建组件
componentFactory.createFromPreset('player', entity);

// 创建特定组件
componentFactory.createPlayerComponents(entity, {
    position: new Vector3(0, 1, 0),
    color: new Color3(1, 0, 0)
});
```

### EntityBuilder
流畅的实体创建API。

```javascript
const entity = builder
    .create('spaceship')
    .at(0, 10, 0)
    .rotatedBy(0, Math.PI, 0)
    .withRender({ meshType: 'box' })
    .withPhysics({ mass: 50 })
    .withTags('vehicle', 'player')
    .build();
```

## 🎯 预设模板

系统提供了多种预设模板，可以快速创建常见类型的实体：

- `BASIC` - 基础实体（仅Transform）
- `VISIBLE` - 可见实体（Transform + Render）
- `PHYSICS` - 物理实体（Transform + Render + Physics）
- `PLAYER` - 玩家实体（完整功能）
- `NPC` - NPC实体（AI就绪）
- `STATIC` - 静态物体（环境物体）
- `TRIGGER` - 触发器（碰撞检测）

## 🔧 高级功能

### 实体查询

```javascript
// 查询具有特定组件的实体
const physicsEntities = ecs.manager.queryEntities('PhysicsComponent');

// 查询具有多个组件的实体
const renderablePhysics = ecs.manager.queryEntities(
    'RenderComponent', 
    'PhysicsComponent'
);
```

### 事件系统

```javascript
// 监听实体事件
entity.on('componentAdded', (data) => {
    console.log(`组件已添加: ${data.componentType}`);
});

// 监听系统事件
physicsSystem.on('collision', (data) => {
    console.log('碰撞发生:', data.entity1.id, data.entity2.id);
});
```

### 性能监控

```javascript
// 获取性能统计
const stats = ecs.manager.getPerformanceStats();
console.log(`FPS: ${stats.fps}, 实体数: ${stats.entityCount}`);

// 获取系统性能
const renderStats = ecs.systems.render.getRenderStats();
console.log(`绘制调用: ${renderStats.totalDrawCalls}`);
```

## 🧪 测试

运行单元测试：

```bash
npm test src/ecs/
```

测试覆盖了：
- 实体生命周期管理
- 组件添加/移除
- 系统更新逻辑
- ECS管理器功能
- 性能统计

## 📚 文档

详细文档位于 `doc/` 目录：

- [架构设计文档](../../doc/ecs-architecture.md) - 深入了解ECS架构设计
- [API参考文档](../../doc/ecs-api-reference.md) - 完整的API文档
- [使用指南](../../doc/ecs-usage-guide.md) - 详细的使用教程和最佳实践

## 🔗 集成示例

查看 `integration-example.js` 了解如何将ECS系统集成到现有的SceneManager中：

```javascript
import { integrateECSWithSceneManager } from './src/ecs/integration-example.js';

// 集成ECS到场景管理器
const ecsIntegration = integrateECSWithSceneManager(sceneManager);

// ECS系统现在会自动随场景管理器更新
```

## ⚡ 性能优化

### 内存管理
- 使用对象池重用频繁创建/销毁的对象
- 及时清理已销毁的实体和组件
- 避免循环引用

### 更新优化
- 设置系统优先级控制更新顺序
- 使用不同的更新频率优化性能
- 批量处理相似操作

### 渲染优化
- 启用视锥体剔除减少绘制调用
- 使用LOD系统优化远距离渲染
- 合理使用批处理和实例化

## 🤝 贡献

1. 遵循现有的代码风格和注释规范
2. 为新功能添加单元测试
3. 更新相关文档
4. 确保性能不会显著下降

## 📄 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

感谢所有为《Cantos》项目ECS系统开发做出贡献的开发者。

---

**注意**：这是一个生产级的ECS实现，专为《Cantos》项目设计。所有代码都包含详细的中文注释，便于团队协作和维护。
