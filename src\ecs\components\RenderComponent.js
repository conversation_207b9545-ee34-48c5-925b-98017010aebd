// src/ecs/components/RenderComponent.js
// 渲染组件 - 管理实体的渲染相关属性和Babylon.js网格

import { 
    MeshBuilder, 
    StandardMaterial, 
    Color3, 
    Vector3,
    Texture,
    PBRMaterial
} from '@babylonjs/core';
import Component from '../Component.js';

/**
 * 渲染组件类
 * 管理实体的视觉表现，包括网格、材质、纹理等
 * 依赖TransformComponent来获取位置信息
 */
export class RenderComponent extends Component {
    /**
     * 构造函数
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     */
    constructor(entity = null, options = {}) {
        super(entity, options);
        
        // 设置依赖组件
        this.dependencies = ['TransformComponent'];
        
        // Babylon.js网格对象
        this.mesh = null;
        
        // 材质对象
        this.material = null;
        
        // 渲染配置
        this.renderConfig = {
            meshType: options.meshType || 'box',           // 网格类型
            meshOptions: options.meshOptions || {},        // 网格创建选项
            materialType: options.materialType || 'standard', // 材质类型
            materialOptions: options.materialOptions || {}, // 材质选项
            visible: options.visible !== false,            // 是否可见
            castShadows: options.castShadows !== false,    // 是否投射阴影
            receiveShadows: options.receiveShadows !== false, // 是否接收阴影
            renderingGroupId: options.renderingGroupId || 0, // 渲染组ID
            alphaIndex: options.alphaIndex || 0,           // Alpha排序索引
            layerMask: options.layerMask || 0x0FFFFFFF     // 层遮罩
        };
        
        // 纹理配置
        this.textureConfig = {
            diffuseTexture: options.diffuseTexture || null,
            normalTexture: options.normalTexture || null,
            specularTexture: options.specularTexture || null,
            emissiveTexture: options.emissiveTexture || null,
            bumpTexture: options.bumpTexture || null
        };
        
        // 颜色配置
        this.colorConfig = {
            diffuseColor: options.diffuseColor || new Color3(1, 1, 1),
            specularColor: options.specularColor || new Color3(1, 1, 1),
            emissiveColor: options.emissiveColor || new Color3(0, 0, 0),
            ambientColor: options.ambientColor || new Color3(0, 0, 0)
        };
        
        // LOD (Level of Detail) 配置
        this.lodConfig = {
            enabled: options.enableLOD || false,
            distances: options.lodDistances || [10, 50, 100],
            meshes: options.lodMeshes || []
        };
        
        // 动画配置
        this.animationConfig = {
            autoPlay: options.autoPlay || false,
            loop: options.loop || false,
            speed: options.speed || 1.0
        };
        
        // 渲染状态
        this.isVisible = this.renderConfig.visible;
        this.isLoaded = false;
        this.needsUpdate = false;
        
        // 性能统计
        this.renderStats = {
            triangleCount: 0,
            vertexCount: 0,
            drawCalls: 1,
            lastRenderTime: 0
        };
        
        console.log(`渲染组件已创建: ${this.entity?.id}`);
    }
    
    /**
     * 组件初始化
     */
    onInitialize() {
        // 获取场景引用
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent) {
            console.error(`渲染组件缺少TransformComponent依赖: ${this.entity?.id}`);
            return;
        }
        
        // 创建网格和材质
        this.createMesh();
        this.createMaterial();
        this.applyMaterial();
        
        // 同步变换
        this.syncTransform();
        
        // 监听变换变化
        transformComponent.on('transformChanged', () => {
            this.syncTransform();
        });
        
        this.isLoaded = true;
        
        // 触发渲染组件就绪事件
        this.emit('renderReady', { component: this });
    }
    
    /**
     * 创建网格
     * @private
     */
    createMesh() {
        if (!this.entity || !this.entity.scene) {
            console.error('无法创建网格：缺少场景引用');
            return;
        }
        
        const scene = this.entity.scene;
        const { meshType, meshOptions } = this.renderConfig;
        
        try {
            // 根据网格类型创建网格
            switch (meshType) {
                case 'box':
                    this.mesh = MeshBuilder.CreateBox(
                        `${this.entity.id}_mesh`,
                        { size: 1, ...meshOptions },
                        scene
                    );
                    break;
                    
                case 'sphere':
                    this.mesh = MeshBuilder.CreateSphere(
                        `${this.entity.id}_mesh`,
                        { diameter: 1, ...meshOptions },
                        scene
                    );
                    break;
                    
                case 'cylinder':
                    this.mesh = MeshBuilder.CreateCylinder(
                        `${this.entity.id}_mesh`,
                        { height: 2, diameter: 1, ...meshOptions },
                        scene
                    );
                    break;
                    
                case 'plane':
                    this.mesh = MeshBuilder.CreatePlane(
                        `${this.entity.id}_mesh`,
                        { size: 1, ...meshOptions },
                        scene
                    );
                    break;
                    
                case 'ground':
                    this.mesh = MeshBuilder.CreateGround(
                        `${this.entity.id}_mesh`,
                        { width: 1, height: 1, ...meshOptions },
                        scene
                    );
                    break;
                    
                case 'capsule':
                    this.mesh = MeshBuilder.CreateCapsule(
                        `${this.entity.id}_mesh`,
                        { radius: 0.5, height: 2, ...meshOptions },
                        scene
                    );
                    break;
                    
                default:
                    console.warn(`未知的网格类型: ${meshType}，使用默认box`);
                    this.mesh = MeshBuilder.CreateBox(
                        `${this.entity.id}_mesh`,
                        { size: 1, ...meshOptions },
                        scene
                    );
            }
            
            // 设置网格属性
            this.mesh.setEnabled(this.renderConfig.visible);
            this.mesh.renderingGroupId = this.renderConfig.renderingGroupId;
            this.mesh.alphaIndex = this.renderConfig.alphaIndex;
            this.mesh.layerMask = this.renderConfig.layerMask;
            
            // 设置阴影属性
            if (this.renderConfig.castShadows) {
                // 这里需要根据场景的阴影生成器来设置
                // scene.shadowGenerators?.forEach(sg => sg.addShadowCaster(this.mesh));
            }
            
            this.mesh.receiveShadows = this.renderConfig.receiveShadows;
            
            // 更新渲染统计
            this.updateRenderStats();
            
            console.log(`网格已创建: ${meshType} for ${this.entity?.id}`);
            
        } catch (error) {
            console.error(`创建网格失败: ${meshType}`, error);
        }
    }
    
    /**
     * 创建材质
     * @private
     */
    createMaterial() {
        if (!this.entity || !this.entity.scene) {
            console.error('无法创建材质：缺少场景引用');
            return;
        }
        
        const scene = this.entity.scene;
        const { materialType, materialOptions } = this.renderConfig;
        
        try {
            switch (materialType) {
                case 'standard':
                    this.material = new StandardMaterial(
                        `${this.entity.id}_material`,
                        scene
                    );
                    this.setupStandardMaterial();
                    break;
                    
                case 'pbr':
                    this.material = new PBRMaterial(
                        `${this.entity.id}_material`,
                        scene
                    );
                    this.setupPBRMaterial();
                    break;
                    
                default:
                    console.warn(`未知的材质类型: ${materialType}，使用默认standard`);
                    this.material = new StandardMaterial(
                        `${this.entity.id}_material`,
                        scene
                    );
                    this.setupStandardMaterial();
            }
            
            // 应用材质选项
            Object.assign(this.material, materialOptions);
            
            console.log(`材质已创建: ${materialType} for ${this.entity?.id}`);
            
        } catch (error) {
            console.error(`创建材质失败: ${materialType}`, error);
        }
    }
    
    /**
     * 设置标准材质
     * @private
     */
    setupStandardMaterial() {
        if (!(this.material instanceof StandardMaterial)) return;
        
        // 设置颜色
        this.material.diffuseColor = this.colorConfig.diffuseColor;
        this.material.specularColor = this.colorConfig.specularColor;
        this.material.emissiveColor = this.colorConfig.emissiveColor;
        this.material.ambientColor = this.colorConfig.ambientColor;
        
        // 设置纹理
        this.applyTextures();
    }
    
    /**
     * 设置PBR材质
     * @private
     */
    setupPBRMaterial() {
        if (!(this.material instanceof PBRMaterial)) return;
        
        // 设置基础颜色
        this.material.baseColor = this.colorConfig.diffuseColor;
        this.material.emissiveColor = this.colorConfig.emissiveColor;
        
        // 设置PBR属性
        this.material.metallicFactor = this.renderConfig.materialOptions.metallicFactor || 0.0;
        this.material.roughnessFactor = this.renderConfig.materialOptions.roughnessFactor || 1.0;
        
        // 设置纹理
        this.applyTextures();
    }
    
    /**
     * 应用纹理
     * @private
     */
    applyTextures() {
        if (!this.material || !this.entity?.scene) return;
        
        const scene = this.entity.scene;
        
        // 应用漫反射纹理
        if (this.textureConfig.diffuseTexture) {
            const texture = new Texture(this.textureConfig.diffuseTexture, scene);
            if (this.material instanceof StandardMaterial) {
                this.material.diffuseTexture = texture;
            } else if (this.material instanceof PBRMaterial) {
                this.material.baseTexture = texture;
            }
        }
        
        // 应用法线纹理
        if (this.textureConfig.normalTexture) {
            const texture = new Texture(this.textureConfig.normalTexture, scene);
            this.material.bumpTexture = texture;
        }
        
        // 应用其他纹理...
        // 这里可以根据需要添加更多纹理类型的处理
    }
    
    /**
     * 应用材质到网格
     * @private
     */
    applyMaterial() {
        if (this.mesh && this.material) {
            this.mesh.material = this.material;
        }
    }
    
    /**
     * 同步变换组件的变换到网格
     * @private
     */
    syncTransform() {
        const transformComponent = this.getDependency('TransformComponent');
        if (!transformComponent || !this.mesh) return;
        
        // 同步位置
        this.mesh.position.copyFrom(transformComponent.position);
        
        // 同步旋转
        this.mesh.rotationQuaternion = transformComponent.rotation.clone();
        
        // 同步缩放
        this.mesh.scaling.copyFrom(transformComponent.scale);
    }
    
    /**
     * 设置可见性
     * @param {boolean} visible - 是否可见
     */
    setVisible(visible) {
        this.isVisible = visible;
        this.renderConfig.visible = visible;
        
        if (this.mesh) {
            this.mesh.setEnabled(visible);
        }
        
        this.emit('visibilityChanged', { component: this, visible });
    }
    
    /**
     * 设置材质颜色
     * @param {string} colorType - 颜色类型 ('diffuse', 'specular', 'emissive', 'ambient')
     * @param {Color3} color - 颜色值
     */
    setColor(colorType, color) {
        if (!this.colorConfig.hasOwnProperty(colorType + 'Color')) {
            console.warn(`未知的颜色类型: ${colorType}`);
            return;
        }
        
        this.colorConfig[colorType + 'Color'] = color.clone();
        
        // 应用到材质
        if (this.material) {
            if (this.material instanceof StandardMaterial) {
                this.material[colorType + 'Color'] = color;
            } else if (this.material instanceof PBRMaterial && colorType === 'diffuse') {
                this.material.baseColor = color;
            }
        }
        
        this.emit('colorChanged', { component: this, colorType, color });
    }
    
    /**
     * 设置纹理
     * @param {string} textureType - 纹理类型
     * @param {string} textureUrl - 纹理URL
     */
    setTexture(textureType, textureUrl) {
        if (!this.textureConfig.hasOwnProperty(textureType)) {
            console.warn(`未知的纹理类型: ${textureType}`);
            return;
        }
        
        this.textureConfig[textureType] = textureUrl;
        
        // 重新应用纹理
        this.applyTextures();
        
        this.emit('textureChanged', { component: this, textureType, textureUrl });
    }
    
    /**
     * 更新渲染统计
     * @private
     */
    updateRenderStats() {
        if (!this.mesh) return;
        
        const geometry = this.mesh.geometry;
        if (geometry) {
            this.renderStats.vertexCount = geometry.getTotalVertices();
            this.renderStats.triangleCount = geometry.getTotalIndices() / 3;
        }
        
        this.renderStats.lastRenderTime = performance.now();
    }
    
    /**
     * 获取网格边界信息
     * @returns {Object} 边界信息
     */
    getBoundingInfo() {
        if (!this.mesh) return null;
        
        const boundingInfo = this.mesh.getBoundingInfo();
        return {
            min: boundingInfo.minimum,
            max: boundingInfo.maximum,
            center: boundingInfo.boundingBox.center,
            size: boundingInfo.boundingBox.maximum.subtract(boundingInfo.boundingBox.minimum)
        };
    }
    
    /**
     * 组件更新
     * @param {number} deltaTime - 时间间隔
     */
    onUpdate(deltaTime) {
        if (!this.isLoaded || !this.mesh) return;
        
        // 如果需要更新，同步变换
        if (this.needsUpdate) {
            this.syncTransform();
            this.needsUpdate = false;
        }
        
        // 更新LOD（如果启用）
        if (this.lodConfig.enabled) {
            this.updateLOD();
        }
    }
    
    /**
     * 更新LOD
     * @private
     */
    updateLOD() {
        // LOD实现需要相机距离计算
        // 这里是简化版本，实际实现需要更复杂的逻辑
        if (!this.entity?.scene?.activeCamera) return;
        
        const camera = this.entity.scene.activeCamera;
        const distance = Vector3.Distance(camera.position, this.mesh.position);
        
        // 根据距离选择合适的LOD级别
        // 这里需要实际的LOD网格切换逻辑
    }
    
    /**
     * 序列化组件数据
     * @returns {Object} 序列化数据
     */
    onSerialize() {
        return {
            renderConfig: this.renderConfig,
            textureConfig: this.textureConfig,
            colorConfig: {
                diffuseColor: this.colorConfig.diffuseColor.asArray(),
                specularColor: this.colorConfig.specularColor.asArray(),
                emissiveColor: this.colorConfig.emissiveColor.asArray(),
                ambientColor: this.colorConfig.ambientColor.asArray()
            },
            lodConfig: this.lodConfig,
            animationConfig: this.animationConfig,
            isVisible: this.isVisible
        };
    }
    
    /**
     * 反序列化组件数据
     * @param {Object} data - 序列化数据
     */
    onDeserialize(data) {
        if (data.renderConfig) {
            this.renderConfig = { ...this.renderConfig, ...data.renderConfig };
        }
        
        if (data.textureConfig) {
            this.textureConfig = { ...this.textureConfig, ...data.textureConfig };
        }
        
        if (data.colorConfig) {
            this.colorConfig.diffuseColor = Color3.FromArray(data.colorConfig.diffuseColor);
            this.colorConfig.specularColor = Color3.FromArray(data.colorConfig.specularColor);
            this.colorConfig.emissiveColor = Color3.FromArray(data.colorConfig.emissiveColor);
            this.colorConfig.ambientColor = Color3.FromArray(data.colorConfig.ambientColor);
        }
        
        if (data.isVisible !== undefined) {
            this.setVisible(data.isVisible);
        }
        
        // 重新创建网格和材质
        if (this.isInitialized) {
            this.createMesh();
            this.createMaterial();
            this.applyMaterial();
        }
    }
    
    /**
     * 组件清理
     */
    onCleanup() {
        // 清理网格
        if (this.mesh) {
            this.mesh.dispose();
            this.mesh = null;
        }
        
        // 清理材质
        if (this.material) {
            this.material.dispose();
            this.material = null;
        }
        
        this.isLoaded = false;
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            isVisible: this.isVisible,
            isLoaded: this.isLoaded,
            meshType: this.renderConfig.meshType,
            materialType: this.renderConfig.materialType,
            renderStats: this.renderStats,
            boundingInfo: this.getBoundingInfo()
        };
    }
}

export default RenderComponent;
