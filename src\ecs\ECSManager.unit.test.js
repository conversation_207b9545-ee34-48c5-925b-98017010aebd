// src/ecs/ECSManager.unit.test.js
// ECSManager类的单元测试

import ECSManager from './ECSManager.js';
import Entity from './Entity.js';
import Component from './Component.js';
import System from './System.js';

// 创建测试用的组件类
class TestComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.testValue = options.testValue || 'default';
    }
}

class AnotherTestComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.anotherValue = options.anotherValue || 42;
    }
}

// 创建测试用的系统类
class TestSystem extends System {
    constructor(scene, options = {}) {
        super(scene, options);
        this.requiredComponents = ['TestComponent'];
        this.updateCount = 0;
        this.entitiesProcessed = [];
    }
    
    onUpdate(deltaTime) {
        this.updateCount++;
        this.entitiesProcessed = Array.from(this.entities);
    }
    
    updateEntity(entity, deltaTime) {
        const component = entity.getComponent('TestComponent');
        if (component) {
            component.lastUpdateTime = deltaTime;
        }
    }
}

// 模拟Babylon.js场景
const mockScene = {
    dispose: jest.fn()
};

describe('ECSManager', () => {
    let ecsManager;
    
    beforeEach(() => {
        ecsManager = new ECSManager(mockScene);
    });
    
    afterEach(() => {
        if (ecsManager) {
            ecsManager.cleanup();
        }
    });
    
    describe('构造函数', () => {
        test('应该正确创建ECS管理器', () => {
            expect(ecsManager.scene).toBe(mockScene);
            expect(ecsManager.entities).toBeInstanceOf(Map);
            expect(ecsManager.systems).toBeInstanceOf(Map);
            expect(ecsManager.componentTypes).toBeInstanceOf(Map);
            expect(ecsManager.isRunning).toBe(false);
            expect(ecsManager.isPaused).toBe(false);
            expect(ecsManager.entityIdCounter).toBe(0);
        });
        
        test('应该设置默认选项', () => {
            expect(ecsManager.options.maxEntities).toBe(10000);
            expect(ecsManager.options.enablePerformanceMonitoring).toBe(true);
            expect(ecsManager.options.updateBatchSize).toBe(100);
        });
        
        test('应该能覆盖默认选项', () => {
            const customManager = new ECSManager(mockScene, {
                maxEntities: 5000,
                enablePerformanceMonitoring: false
            });
            
            expect(customManager.options.maxEntities).toBe(5000);
            expect(customManager.options.enablePerformanceMonitoring).toBe(false);
            
            customManager.cleanup();
        });
    });
    
    describe('生命周期管理', () => {
        test('应该能初始化', () => {
            const mockEmit = jest.spyOn(ecsManager, 'emit');
            
            ecsManager.initialize();
            
            expect(mockEmit).toHaveBeenCalledWith('initialized', { manager: ecsManager });
        });
        
        test('应该能启动和停止', () => {
            const mockEmit = jest.spyOn(ecsManager, 'emit');
            
            ecsManager.start();
            expect(ecsManager.isRunning).toBe(true);
            expect(ecsManager.isPaused).toBe(false);
            expect(mockEmit).toHaveBeenCalledWith('started', { manager: ecsManager });
            
            ecsManager.stop();
            expect(ecsManager.isRunning).toBe(false);
            expect(mockEmit).toHaveBeenCalledWith('stopped', { manager: ecsManager });
        });
        
        test('应该能暂停和恢复', () => {
            const mockEmit = jest.spyOn(ecsManager, 'emit');
            
            ecsManager.start();
            
            ecsManager.pause();
            expect(ecsManager.isPaused).toBe(true);
            expect(mockEmit).toHaveBeenCalledWith('paused', { manager: ecsManager });
            
            ecsManager.resume();
            expect(ecsManager.isPaused).toBe(false);
            expect(mockEmit).toHaveBeenCalledWith('resumed', { manager: ecsManager });
        });
        
        test('重复启动应该被忽略', () => {
            ecsManager.start();
            const mockEmit = jest.spyOn(ecsManager, 'emit');
            
            ecsManager.start(); // 重复启动
            
            expect(mockEmit).not.toHaveBeenCalledWith('started', expect.any(Object));
        });
    });
    
    describe('实体管理', () => {
        test('应该能创建实体', () => {
            const entity = ecsManager.createEntity('test-entity');
            
            expect(entity).toBeInstanceOf(Entity);
            expect(entity.id).toBe('entity_1');
            expect(entity.name).toBe('test-entity');
            expect(ecsManager.entities.has(entity.id)).toBe(true);
            expect(ecsManager.entityIdCounter).toBe(1);
        });
        
        test('应该能获取实体', () => {
            const entity = ecsManager.createEntity('test-entity');
            const retrieved = ecsManager.getEntity(entity.id);
            
            expect(retrieved).toBe(entity);
        });
        
        test('获取不存在的实体应该返回null', () => {
            const retrieved = ecsManager.getEntity('nonexistent');
            expect(retrieved).toBeNull();
        });
        
        test('应该能移除实体', () => {
            const entity = ecsManager.createEntity('test-entity');
            const entityId = entity.id;
            
            const result = ecsManager.removeEntity(entityId);
            
            expect(result).toBe(true);
            expect(ecsManager.entities.has(entityId)).toBe(false);
            expect(entity.isDestroyed).toBe(true);
        });
        
        test('移除不存在的实体应该返回false', () => {
            const result = ecsManager.removeEntity('nonexistent');
            expect(result).toBe(false);
        });
        
        test('应该能获取所有实体', () => {
            const entity1 = ecsManager.createEntity('entity1');
            const entity2 = ecsManager.createEntity('entity2');
            
            const allEntities = ecsManager.getAllEntities();
            
            expect(allEntities).toHaveLength(2);
            expect(allEntities).toContain(entity1);
            expect(allEntities).toContain(entity2);
        });
        
        test('应该限制最大实体数量', () => {
            const limitedManager = new ECSManager(mockScene, { maxEntities: 2 });
            
            const entity1 = limitedManager.createEntity('entity1');
            const entity2 = limitedManager.createEntity('entity2');
            const entity3 = limitedManager.createEntity('entity3');
            
            expect(entity1).toBeInstanceOf(Entity);
            expect(entity2).toBeInstanceOf(Entity);
            expect(entity3).toBeNull();
            
            limitedManager.cleanup();
        });
    });
    
    describe('系统管理', () => {
        let testSystem;
        
        beforeEach(() => {
            testSystem = new TestSystem(mockScene);
        });
        
        test('应该能添加系统', () => {
            const mockEmit = jest.spyOn(ecsManager, 'emit');
            
            ecsManager.addSystem(testSystem);
            
            expect(ecsManager.systems.has('TestSystem')).toBe(true);
            expect(ecsManager.getSystem('TestSystem')).toBe(testSystem);
            expect(mockEmit).toHaveBeenCalledWith('systemAdded', {
                manager: ecsManager,
                system: testSystem,
                name: 'TestSystem'
            });
        });
        
        test('应该能使用自定义名称添加系统', () => {
            ecsManager.addSystem(testSystem, 'CustomName');
            
            expect(ecsManager.systems.has('CustomName')).toBe(true);
            expect(ecsManager.getSystem('CustomName')).toBe(testSystem);
        });
        
        test('应该能移除系统', () => {
            ecsManager.addSystem(testSystem);
            const mockDispose = jest.spyOn(testSystem, 'dispose');
            const mockEmit = jest.spyOn(ecsManager, 'emit');
            
            const result = ecsManager.removeSystem('TestSystem');
            
            expect(result).toBe(true);
            expect(ecsManager.systems.has('TestSystem')).toBe(false);
            expect(mockDispose).toHaveBeenCalled();
            expect(mockEmit).toHaveBeenCalledWith('systemRemoved', {
                manager: ecsManager,
                system: testSystem,
                name: 'TestSystem'
            });
        });
        
        test('移除不存在的系统应该返回false', () => {
            const result = ecsManager.removeSystem('NonexistentSystem');
            expect(result).toBe(false);
        });
        
        test('应该能获取所有系统', () => {
            const anotherSystem = new TestSystem(mockScene);
            
            ecsManager.addSystem(testSystem, 'System1');
            ecsManager.addSystem(anotherSystem, 'System2');
            
            const allSystems = ecsManager.getAllSystems();
            
            expect(allSystems).toHaveLength(2);
            expect(allSystems).toContain(testSystem);
            expect(allSystems).toContain(anotherSystem);
        });
        
        test('启动时应该初始化系统', () => {
            const mockInitialize = jest.spyOn(testSystem, 'initialize');
            
            ecsManager.addSystem(testSystem);
            ecsManager.start();
            
            expect(mockInitialize).toHaveBeenCalled();
        });
    });
    
    describe('组件注册', () => {
        test('应该能注册组件类型', () => {
            ecsManager.registerComponent(TestComponent);
            
            expect(ecsManager.componentTypes.has('TestComponent')).toBe(true);
            expect(ecsManager.getComponentType('TestComponent')).toBe(TestComponent);
        });
        
        test('应该能使用自定义名称注册组件', () => {
            ecsManager.registerComponent(TestComponent, 'CustomComponent');
            
            expect(ecsManager.componentTypes.has('CustomComponent')).toBe(true);
            expect(ecsManager.getComponentType('CustomComponent')).toBe(TestComponent);
        });
        
        test('获取未注册的组件类型应该返回null', () => {
            const result = ecsManager.getComponentType('NonexistentComponent');
            expect(result).toBeNull();
        });
    });
    
    describe('实体查询', () => {
        let entity1, entity2, entity3;
        
        beforeEach(() => {
            ecsManager.registerComponent(TestComponent);
            ecsManager.registerComponent(AnotherTestComponent);
            
            entity1 = ecsManager.createEntity('entity1');
            entity2 = ecsManager.createEntity('entity2');
            entity3 = ecsManager.createEntity('entity3');
            
            entity1.addComponent(new TestComponent(entity1));
            entity2.addComponent(new TestComponent(entity2));
            entity2.addComponent(new AnotherTestComponent(entity2));
            entity3.addComponent(new AnotherTestComponent(entity3));
        });
        
        test('应该能查询具有特定组件的实体', () => {
            const entitiesWithTest = ecsManager.queryEntities('TestComponent');
            const entitiesWithAnother = ecsManager.queryEntities('AnotherTestComponent');
            
            expect(entitiesWithTest).toHaveLength(2);
            expect(entitiesWithTest).toContain(entity1);
            expect(entitiesWithTest).toContain(entity2);
            
            expect(entitiesWithAnother).toHaveLength(2);
            expect(entitiesWithAnother).toContain(entity2);
            expect(entitiesWithAnother).toContain(entity3);
        });
        
        test('应该能查询具有多个组件的实体', () => {
            const entitiesWithBoth = ecsManager.queryEntities('TestComponent', 'AnotherTestComponent');
            
            expect(entitiesWithBoth).toHaveLength(1);
            expect(entitiesWithBoth).toContain(entity2);
        });
        
        test('应该能使用组件类进行查询', () => {
            const entitiesWithTest = ecsManager.queryEntities(TestComponent);
            
            expect(entitiesWithTest).toHaveLength(2);
            expect(entitiesWithTest).toContain(entity1);
            expect(entitiesWithTest).toContain(entity2);
        });
    });
    
    describe('更新循环', () => {
        let testSystem;
        let entity;
        
        beforeEach(() => {
            testSystem = new TestSystem(mockScene);
            ecsManager.addSystem(testSystem);
            ecsManager.registerComponent(TestComponent);
            
            entity = ecsManager.createEntity('test-entity');
            entity.addComponent(new TestComponent(entity));
            
            ecsManager.start();
        });
        
        test('应该更新所有系统', () => {
            ecsManager.update();
            
            expect(testSystem.updateCount).toBe(1);
            expect(testSystem.entitiesProcessed).toContain(entity);
        });
        
        test('暂停时不应该更新', () => {
            ecsManager.pause();
            ecsManager.update();
            
            expect(testSystem.updateCount).toBe(0);
        });
        
        test('停止时不应该更新', () => {
            ecsManager.stop();
            ecsManager.update();
            
            expect(testSystem.updateCount).toBe(0);
        });
        
        test('应该清理已销毁的实体', () => {
            entity.destroy();
            ecsManager.update();
            
            expect(ecsManager.entities.has(entity.id)).toBe(false);
        });
    });
    
    describe('性能统计', () => {
        test('应该提供性能统计信息', () => {
            const stats = ecsManager.getPerformanceStats();
            
            expect(stats).toHaveProperty('frameCount');
            expect(stats).toHaveProperty('totalUpdateTime');
            expect(stats).toHaveProperty('averageUpdateTime');
            expect(stats).toHaveProperty('entityCount');
            expect(stats).toHaveProperty('systemCount');
            expect(stats).toHaveProperty('isRunning');
            expect(stats).toHaveProperty('isPaused');
        });
        
        test('应该能重置性能统计', () => {
            ecsManager.update(); // 产生一些统计数据
            
            ecsManager.resetPerformanceStats();
            const stats = ecsManager.getPerformanceStats();
            
            expect(stats.frameCount).toBe(0);
            expect(stats.totalUpdateTime).toBe(0);
            expect(stats.averageUpdateTime).toBe(0);
        });
    });
    
    describe('清理', () => {
        test('应该清理所有资源', () => {
            const testSystem = new TestSystem(mockScene);
            const entity = ecsManager.createEntity('test-entity');
            
            ecsManager.addSystem(testSystem);
            ecsManager.registerComponent(TestComponent);
            ecsManager.start();
            
            const mockSystemDispose = jest.spyOn(testSystem, 'dispose');
            const mockEntityDestroy = jest.spyOn(entity, 'destroy');
            
            ecsManager.cleanup();
            
            expect(ecsManager.isRunning).toBe(false);
            expect(ecsManager.systems.size).toBe(0);
            expect(ecsManager.entities.size).toBe(0);
            expect(ecsManager.componentTypes.size).toBe(0);
            expect(ecsManager.entityIdCounter).toBe(0);
            expect(mockSystemDispose).toHaveBeenCalled();
            expect(mockEntityDestroy).toHaveBeenCalled();
        });
    });
    
    describe('调试信息', () => {
        test('应该提供调试信息', () => {
            const testSystem = new TestSystem(mockScene);
            const entity = ecsManager.createEntity('test-entity');
            
            ecsManager.addSystem(testSystem);
            ecsManager.registerComponent(TestComponent);
            
            const debugInfo = ecsManager.getDebugInfo();
            
            expect(debugInfo).toHaveProperty('isRunning');
            expect(debugInfo).toHaveProperty('isPaused');
            expect(debugInfo).toHaveProperty('entityCount', 1);
            expect(debugInfo).toHaveProperty('systemCount', 1);
            expect(debugInfo).toHaveProperty('componentTypeCount', 1);
            expect(debugInfo).toHaveProperty('performanceStats');
            expect(debugInfo).toHaveProperty('systemUpdateOrder');
            expect(debugInfo).toHaveProperty('options');
        });
    });
});
