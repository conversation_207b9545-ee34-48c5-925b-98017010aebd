// src/ecs/index.js
// ECS系统主入口文件 - 导出所有ECS相关的类和工具

// 核心类
import { Entity } from './Entity.js';
import { Component } from './Component.js';
import { System } from './System.js';
import { ECSManager } from './ECSManager.js';
import { ComponentRegistry, componentRegistry } from './ComponentRegistry.js';

// 重新导出核心类
export { Entity, Component, System, ECSManager, ComponentRegistry, componentRegistry };

// 预定义组件
import { TransformComponent } from './components/TransformComponent.js';
import { RenderComponent } from './components/RenderComponent.js';
import { PhysicsComponent } from './components/PhysicsComponent.js';
import { AnimationComponent } from './components/AnimationComponent.js';
import { NetworkComponent } from './components/NetworkComponent.js';

// 核心系统
import { RenderSystem } from './systems/RenderSystem.js';
import { PhysicsSystem } from './systems/PhysicsSystem.js';

// 工具类
import { ComponentFactory, componentFactory } from './utils/ComponentFactory.js';
import { EntityBuilder } from './utils/EntityBuilder.js';

// 重新导出组件
export { TransformComponent, RenderComponent, PhysicsComponent, AnimationComponent, NetworkComponent };

// 重新导出系统
export { RenderSystem, PhysicsSystem };

// 重新导出工具类
export { ComponentFactory, componentFactory, EntityBuilder };

/**
 * 创建ECS管理器实例
 * @param {BABYLON.Scene} scene - Babylon.js场景
 * @param {Object} [options={}] - 配置选项
 * @returns {ECSManager} ECS管理器实例
 */
export function createECSManager(scene, options = {}) {
    return new ECSManager(scene, options);
}

/**
 * 创建实体构建器
 * @param {ECSManager} ecsManager - ECS管理器
 * @returns {EntityBuilder} 实体构建器实例
 */
export function createEntityBuilder(ecsManager) {
    return new EntityBuilder(ecsManager);
}

/**
 * 快速设置ECS系统
 * @param {BABYLON.Scene} scene - Babylon.js场景
 * @param {Object} [options={}] - 配置选项
 * @returns {Object} ECS系统对象
 */
export function setupECS(scene, options = {}) {
    // 创建ECS管理器
    const ecsManager = new ECSManager(scene, options.manager);

    // 创建核心系统
    const renderSystem = new RenderSystem(scene, options.render);
    const physicsSystem = new PhysicsSystem(scene, options.physics);

    // 添加系统到管理器
    ecsManager.addSystem(renderSystem);
    ecsManager.addSystem(physicsSystem);

    // 创建实体构建器
    const entityBuilder = new EntityBuilder(ecsManager);

    // 初始化ECS管理器
    ecsManager.initialize();

    console.log('ECS系统设置完成');

    return {
        manager: ecsManager,
        systems: {
            render: renderSystem,
            physics: physicsSystem
        },
        builder: entityBuilder,

        // 便捷方法
        start() {
            ecsManager.start();
        },

        stop() {
            ecsManager.stop();
        },

        update() {
            ecsManager.update();
        },

        createEntity(name) {
            return entityBuilder.create(name);
        },

        cleanup() {
            ecsManager.cleanup();
        }
    };
}

/**
 * ECS系统默认配置
 */
export const ECS_DEFAULT_CONFIG = {
    manager: {
        maxEntities: 10000,
        enablePerformanceMonitoring: true,
        updateBatchSize: 100
    },
    render: {
        enableLOD: true,
        enableFrustumCulling: true,
        enableOcclusionCulling: false,
        enableBatching: false,
        maxDrawCalls: 1000
    },
    physics: {
        gravity: { x: 0, y: -9.81, z: 0 },
        timeStep: 1/60,
        maxSubSteps: 3,
        enableCCD: false,
        enableSleeping: true,
        enableDebugDraw: false
    }
};

/**
 * 常用实体预设配置
 */
export const ENTITY_PRESETS = {
    // 基础实体
    BASIC: 'basic',

    // 可见实体
    VISIBLE: 'visible',

    // 物理实体
    PHYSICS: 'physics',

    // 玩家实体
    PLAYER: 'player',

    // NPC实体
    NPC: 'npc',

    // 静态物体
    STATIC: 'static',

    // 触发器
    TRIGGER: 'trigger',

    // 装饰物
    DECORATION: 'decoration',

    // 粒子效果
    PARTICLE: 'particle',

    // 网络同步物体
    NETWORKED: 'networked'
};

/**
 * 组件类型常量
 */
export const COMPONENT_TYPES = {
    TRANSFORM: 'TransformComponent',
    RENDER: 'RenderComponent',
    PHYSICS: 'PhysicsComponent',
    ANIMATION: 'AnimationComponent',
    NETWORK: 'NetworkComponent'
};

/**
 * 系统类型常量
 */
export const SYSTEM_TYPES = {
    RENDER: 'RenderSystem',
    PHYSICS: 'PhysicsSystem',
    ANIMATION: 'AnimationSystem',
    NETWORK: 'NetworkSystem'
};

/**
 * 网格类型常量
 */
export const MESH_TYPES = {
    BOX: 'box',
    SPHERE: 'sphere',
    CYLINDER: 'cylinder',
    PLANE: 'plane',
    GROUND: 'ground',
    CAPSULE: 'capsule'
};

/**
 * 物理形状类型常量
 */
export const PHYSICS_TYPES = {
    BOX: 'box',
    SPHERE: 'sphere',
    CAPSULE: 'capsule',
    CYLINDER: 'cylinder',
    MESH: 'mesh',
    CONVEX_HULL: 'convexHull'
};

/**
 * 材质类型常量
 */
export const MATERIAL_TYPES = {
    STANDARD: 'standard',
    PBR: 'pbr'
};

/**
 * 验证ECS系统是否正确设置
 * @param {Object} ecsSystem - ECS系统对象
 * @returns {Object} 验证结果
 */
export function validateECSSetup(ecsSystem) {
    const result = {
        valid: true,
        errors: [],
        warnings: []
    };

    // 检查必需的组件
    if (!ecsSystem.manager) {
        result.valid = false;
        result.errors.push('缺少ECS管理器');
    }

    if (!ecsSystem.systems) {
        result.valid = false;
        result.errors.push('缺少系统集合');
    }

    if (!ecsSystem.builder) {
        result.valid = false;
        result.errors.push('缺少实体构建器');
    }

    // 检查系统
    if (ecsSystem.systems) {
        if (!ecsSystem.systems.render) {
            result.warnings.push('缺少渲染系统');
        }

        if (!ecsSystem.systems.physics) {
            result.warnings.push('缺少物理系统');
        }
    }

    // 检查组件注册
    const registeredComponents = componentRegistry.getAllComponentNames();
    const requiredComponents = [
        'TransformComponent',
        'RenderComponent',
        'PhysicsComponent'
    ];

    requiredComponents.forEach(componentType => {
        if (!registeredComponents.includes(componentType)) {
            result.warnings.push(`组件未注册: ${componentType}`);
        }
    });

    return result;
}

/**
 * 获取ECS系统信息
 * @param {Object} ecsSystem - ECS系统对象
 * @returns {Object} 系统信息
 */
export function getECSInfo(ecsSystem) {
    const info = {
        manager: null,
        systems: {},
        components: {},
        entities: 0,
        performance: null
    };

    if (ecsSystem.manager) {
        const debugInfo = ecsSystem.manager.getDebugInfo();
        info.manager = {
            isRunning: debugInfo.isRunning,
            isPaused: debugInfo.isPaused,
            entityCount: debugInfo.entityCount,
            systemCount: debugInfo.systemCount
        };
        info.entities = debugInfo.entityCount;
        info.performance = ecsSystem.manager.getPerformanceStats();
    }

    if (ecsSystem.systems) {
        Object.entries(ecsSystem.systems).forEach(([name, system]) => {
            if (system && typeof system.getDebugInfo === 'function') {
                info.systems[name] = system.getDebugInfo();
            }
        });
    }

    info.components = {
        registered: componentRegistry.getAllComponentNames(),
        groups: componentRegistry.getAllGroups(),
        stats: componentRegistry.getStats()
    };

    return info;
}

/**
 * 创建调试面板数据
 * @param {Object} ecsSystem - ECS系统对象
 * @returns {Object} 调试面板数据
 */
export function createDebugPanelData(ecsSystem) {
    const info = getECSInfo(ecsSystem);

    return {
        title: 'ECS系统调试信息',
        sections: [
            {
                title: '管理器状态',
                data: info.manager
            },
            {
                title: '系统信息',
                data: info.systems
            },
            {
                title: '组件信息',
                data: info.components
            },
            {
                title: '性能统计',
                data: info.performance
            }
        ]
    };
}

// 默认导出
export default {
    // 核心类
    Entity,
    Component,
    System,
    ECSManager,
    ComponentRegistry,
    componentRegistry,

    // 组件
    TransformComponent,
    RenderComponent,
    PhysicsComponent,
    AnimationComponent,
    NetworkComponent,

    // 系统
    RenderSystem,
    PhysicsSystem,

    // 工具
    ComponentFactory,
    componentFactory,
    EntityBuilder,

    // 便捷函数
    createECSManager,
    createEntityBuilder,
    setupECS,
    validateECSSetup,
    getECSInfo,
    createDebugPanelData,

    // 常量
    ECS_DEFAULT_CONFIG,
    ENTITY_PRESETS,
    COMPONENT_TYPES,
    SYSTEM_TYPES,
    MESH_TYPES,
    PHYSICS_TYPES,
    MATERIAL_TYPES
};
