// src/ecs/ECSManager.js
// 实体组件系统管理器
// 负责管理所有实体、组件和系统的生命周期和更新

import { EventEmitter } from '../utils/eventEmitter.js';
import Entity from './Entity.js';

/**
 * ECS管理器类
 * 负责协调实体、组件和系统的运行
 * 提供统一的接口来管理整个ECS架构
 */
export class ECSManager extends EventEmitter {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 管理器初始化选项
     */
    constructor(scene, options = {}) {
        super();
        
        // Babylon.js场景引用
        this.scene = scene;
        
        // 配置选项
        this.options = {
            maxEntities: 10000,           // 最大实体数量
            enablePerformanceMonitoring: true,  // 启用性能监控
            updateBatchSize: 100,         // 批量更新大小
            ...options
        };
        
        // 实体存储
        this.entities = new Map();        // id -> Entity
        this.entityIdCounter = 0;         // 实体ID计数器
        
        // 系统存储
        this.systems = new Map();         // name -> System
        this.systemUpdateOrder = [];      // 系统更新顺序
        
        // 组件注册表
        this.componentTypes = new Map();  // name -> ComponentClass
        
        // 运行状态
        this.isRunning = false;
        this.isPaused = false;
        
        // 性能统计
        this.performanceStats = {
            frameCount: 0,
            totalUpdateTime: 0,
            averageUpdateTime: 0,
            entityCount: 0,
            systemCount: 0,
            lastFrameTime: 0,
            fps: 0
        };
        
        // 时间管理
        this.lastUpdateTime = performance.now();
        this.deltaTime = 0;
        
        // 实体查询缓存
        this.queryCache = new Map();
        this.queryCacheValid = false;
        
        // 延迟操作队列
        this.pendingOperations = [];
        
        console.log("ECS管理器已创建");
    }
    
    /**
     * 初始化ECS管理器
     */
    initialize() {
        console.log("初始化ECS管理器...");
        
        // 清理之前的状态
        this.cleanup();
        
        // 重置统计信息
        this.resetPerformanceStats();
        
        // 触发初始化事件
        this.emit('initialized', { manager: this });
        
        console.log("ECS管理器初始化完成");
    }
    
    /**
     * 启动ECS管理器
     */
    start() {
        if (this.isRunning) {
            console.warn("ECS管理器已经在运行");
            return;
        }
        
        this.isRunning = true;
        this.isPaused = false;
        this.lastUpdateTime = performance.now();
        
        // 初始化所有系统
        this.systems.forEach(system => {
            if (!system.isInitialized) {
                system.initialize();
            }
        });
        
        this.emit('started', { manager: this });
        
        console.log("ECS管理器已启动");
    }
    
    /**
     * 暂停ECS管理器
     */
    pause() {
        if (!this.isRunning || this.isPaused) {
            return;
        }
        
        this.isPaused = true;
        this.emit('paused', { manager: this });
        
        console.log("ECS管理器已暂停");
    }
    
    /**
     * 恢复ECS管理器
     */
    resume() {
        if (!this.isRunning || !this.isPaused) {
            return;
        }
        
        this.isPaused = false;
        this.lastUpdateTime = performance.now();
        this.emit('resumed', { manager: this });
        
        console.log("ECS管理器已恢复");
    }
    
    /**
     * 停止ECS管理器
     */
    stop() {
        if (!this.isRunning) {
            return;
        }
        
        this.isRunning = false;
        this.isPaused = false;
        
        this.emit('stopped', { manager: this });
        
        console.log("ECS管理器已停止");
    }
    
    /**
     * 更新ECS管理器
     * 应该在每帧被调用
     */
    update() {
        if (!this.isRunning || this.isPaused) {
            return;
        }
        
        const currentTime = performance.now();
        this.deltaTime = (currentTime - this.lastUpdateTime) / 1000; // 转换为秒
        this.lastUpdateTime = currentTime;
        
        // 记录性能统计开始时间
        const frameStartTime = performance.now();
        
        // 处理延迟操作
        this.processPendingOperations();
        
        // 清理已销毁的实体
        this.cleanupDestroyedEntities();
        
        // 更新所有系统
        this.updateSystems();
        
        // 更新性能统计
        this.updatePerformanceStats(frameStartTime);
        
        // 触发更新事件
        this.emit('updated', {
            manager: this,
            deltaTime: this.deltaTime,
            frameTime: this.performanceStats.lastFrameTime
        });
    }
    
    /**
     * 更新所有系统
     * @private
     */
    updateSystems() {
        const entitiesSet = new Set(this.entities.values());
        
        // 按优先级顺序更新系统
        this.systemUpdateOrder.forEach(systemName => {
            const system = this.systems.get(systemName);
            if (system && system.enabled && !system.isDestroyed) {
                try {
                    system.update(this.deltaTime, entitiesSet);
                } catch (error) {
                    console.error(`系统更新错误 ${systemName}:`, error);
                    this.emit('systemError', { system, error });
                }
            }
        });
    }
    
    /**
     * 创建新实体
     * @param {string} [name=''] - 实体名称
     * @returns {Entity} 新创建的实体
     */
    createEntity(name = '') {
        if (this.entities.size >= this.options.maxEntities) {
            console.error(`实体数量已达到最大限制: ${this.options.maxEntities}`);
            return null;
        }
        
        const id = this.generateEntityId();
        const entity = new Entity(id, name);
        
        this.entities.set(id, entity);
        
        // 监听实体销毁事件
        entity.on('destroyed', () => {
            this.removeEntity(id);
        });
        
        // 使查询缓存失效
        this.invalidateQueryCache();
        
        this.emit('entityCreated', { manager: this, entity });
        
        console.log(`实体已创建: ${id} (${name})`);
        
        return entity;
    }
    
    /**
     * 获取实体
     * @param {string} entityId - 实体ID
     * @returns {Entity|null} 实体实例
     */
    getEntity(entityId) {
        return this.entities.get(entityId) || null;
    }
    
    /**
     * 移除实体
     * @param {string} entityId - 实体ID
     * @returns {boolean} 是否成功移除
     */
    removeEntity(entityId) {
        const entity = this.entities.get(entityId);
        if (!entity) {
            return false;
        }
        
        // 如果实体还没有被销毁，先销毁它
        if (!entity.isDestroyed) {
            entity.destroy();
        }
        
        // 从存储中移除
        this.entities.delete(entityId);
        
        // 使查询缓存失效
        this.invalidateQueryCache();
        
        this.emit('entityRemoved', { manager: this, entity });
        
        console.log(`实体已移除: ${entityId}`);
        
        return true;
    }
    
    /**
     * 获取所有实体
     * @returns {Array<Entity>} 所有实体的数组
     */
    getAllEntities() {
        return Array.from(this.entities.values());
    }
    
    /**
     * 根据组件类型查询实体
     * @param {...(string|Function)} componentTypes - 组件类型列表
     * @returns {Array<Entity>} 匹配的实体数组
     */
    queryEntities(...componentTypes) {
        // 生成查询缓存键
        const cacheKey = componentTypes.map(type => 
            typeof type === 'string' ? type : type.name
        ).sort().join('|');
        
        // 检查缓存
        if (this.queryCacheValid && this.queryCache.has(cacheKey)) {
            return this.queryCache.get(cacheKey);
        }
        
        // 执行查询
        const results = this.getAllEntities().filter(entity => 
            entity.hasAllComponents(...componentTypes)
        );
        
        // 缓存结果
        this.queryCache.set(cacheKey, results);
        
        return results;
    }
    
    /**
     * 添加系统
     * @param {System} system - 系统实例
     * @param {string} [name] - 系统名称，默认使用类名
     */
    addSystem(system, name = null) {
        const systemName = name || system.constructor.name;
        
        if (this.systems.has(systemName)) {
            console.warn(`系统已存在: ${systemName}`);
            return;
        }
        
        this.systems.set(systemName, system);
        
        // 更新系统更新顺序
        this.updateSystemOrder();
        
        // 如果管理器正在运行，初始化系统
        if (this.isRunning && !system.isInitialized) {
            system.initialize();
        }
        
        this.emit('systemAdded', { manager: this, system, name: systemName });
        
        console.log(`系统已添加: ${systemName}`);
    }
    
    /**
     * 获取系统
     * @param {string} systemName - 系统名称
     * @returns {System|null} 系统实例
     */
    getSystem(systemName) {
        return this.systems.get(systemName) || null;
    }
    
    /**
     * 移除系统
     * @param {string} systemName - 系统名称
     * @returns {boolean} 是否成功移除
     */
    removeSystem(systemName) {
        const system = this.systems.get(systemName);
        if (!system) {
            return false;
        }
        
        // 清理系统
        system.dispose();
        
        // 从存储中移除
        this.systems.delete(systemName);
        
        // 更新系统更新顺序
        this.updateSystemOrder();
        
        this.emit('systemRemoved', { manager: this, system, name: systemName });
        
        console.log(`系统已移除: ${systemName}`);
        
        return true;
    }
    
    /**
     * 获取所有系统
     * @returns {Array<System>} 所有系统的数组
     */
    getAllSystems() {
        return Array.from(this.systems.values());
    }
    
    /**
     * 注册组件类型
     * @param {Function} ComponentClass - 组件类
     * @param {string} [name] - 组件名称，默认使用类名
     */
    registerComponent(ComponentClass, name = null) {
        const componentName = name || ComponentClass.name;
        
        if (this.componentTypes.has(componentName)) {
            console.warn(`组件类型已注册: ${componentName}`);
            return;
        }
        
        this.componentTypes.set(componentName, ComponentClass);
        
        console.log(`组件类型已注册: ${componentName}`);
    }
    
    /**
     * 获取组件类型
     * @param {string} componentName - 组件名称
     * @returns {Function|null} 组件类
     */
    getComponentType(componentName) {
        return this.componentTypes.get(componentName) || null;
    }
    
    /**
     * 生成唯一的实体ID
     * @private
     * @returns {string} 实体ID
     */
    generateEntityId() {
        return `entity_${++this.entityIdCounter}`;
    }
    
    /**
     * 更新系统更新顺序
     * @private
     */
    updateSystemOrder() {
        this.systemUpdateOrder = Array.from(this.systems.keys()).sort((a, b) => {
            const systemA = this.systems.get(a);
            const systemB = this.systems.get(b);
            return systemA.priority - systemB.priority;
        });
    }
    
    /**
     * 使查询缓存失效
     * @private
     */
    invalidateQueryCache() {
        this.queryCacheValid = false;
        this.queryCache.clear();
    }
    
    /**
     * 处理延迟操作
     * @private
     */
    processPendingOperations() {
        const operations = [...this.pendingOperations];
        this.pendingOperations.length = 0;
        
        operations.forEach(operation => {
            try {
                operation();
            } catch (error) {
                console.error("延迟操作执行错误:", error);
            }
        });
    }
    
    /**
     * 清理已销毁的实体
     * @private
     */
    cleanupDestroyedEntities() {
        const destroyedEntities = [];
        
        this.entities.forEach((entity, id) => {
            if (entity.isDestroyed) {
                destroyedEntities.push(id);
            }
        });
        
        destroyedEntities.forEach(id => {
            this.entities.delete(id);
        });
        
        if (destroyedEntities.length > 0) {
            this.invalidateQueryCache();
        }
    }
    
    /**
     * 更新性能统计
     * @private
     * @param {number} frameStartTime - 帧开始时间
     */
    updatePerformanceStats(frameStartTime) {
        const frameEndTime = performance.now();
        const frameTime = frameEndTime - frameStartTime;
        
        this.performanceStats.frameCount++;
        this.performanceStats.totalUpdateTime += frameTime;
        this.performanceStats.averageUpdateTime = 
            this.performanceStats.totalUpdateTime / this.performanceStats.frameCount;
        this.performanceStats.lastFrameTime = frameTime;
        this.performanceStats.entityCount = this.entities.size;
        this.performanceStats.systemCount = this.systems.size;
        
        // 计算FPS（每秒更新一次）
        if (this.performanceStats.frameCount % 60 === 0) {
            this.performanceStats.fps = Math.round(1000 / this.performanceStats.averageUpdateTime);
        }
    }
    
    /**
     * 重置性能统计
     */
    resetPerformanceStats() {
        this.performanceStats = {
            frameCount: 0,
            totalUpdateTime: 0,
            averageUpdateTime: 0,
            entityCount: this.entities.size,
            systemCount: this.systems.size,
            lastFrameTime: 0,
            fps: 0
        };
    }
    
    /**
     * 获取性能统计信息
     * @returns {Object} 性能统计数据
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            isRunning: this.isRunning,
            isPaused: this.isPaused,
            deltaTime: this.deltaTime
        };
    }
    
    /**
     * 清理ECS管理器
     */
    cleanup() {
        console.log("清理ECS管理器...");
        
        // 停止运行
        this.stop();
        
        // 清理所有系统
        this.systems.forEach((system, name) => {
            system.dispose();
        });
        this.systems.clear();
        this.systemUpdateOrder.length = 0;
        
        // 清理所有实体
        this.entities.forEach((entity, id) => {
            entity.destroy();
        });
        this.entities.clear();
        
        // 清理组件注册表
        this.componentTypes.clear();
        
        // 清理缓存
        this.queryCache.clear();
        this.pendingOperations.length = 0;
        
        // 重置计数器
        this.entityIdCounter = 0;
        
        // 清理事件监听器
        this.removeAllListeners();
        
        console.log("ECS管理器清理完成");
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息对象
     */
    getDebugInfo() {
        return {
            isRunning: this.isRunning,
            isPaused: this.isPaused,
            entityCount: this.entities.size,
            systemCount: this.systems.size,
            componentTypeCount: this.componentTypes.size,
            performanceStats: this.performanceStats,
            systemUpdateOrder: this.systemUpdateOrder,
            options: this.options
        };
    }
}

export default ECSManager;
