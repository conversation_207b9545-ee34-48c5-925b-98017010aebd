// src/ecs/systems/RenderSystem.js
// 渲染系统 - 管理所有渲染组件的更新和渲染

import System from '../System.js';

/**
 * 渲染系统类
 * 负责管理所有具有渲染组件的实体的渲染更新
 * 处理LOD、视锥体剔除、批处理等渲染优化
 */
export class RenderSystem extends System {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 系统选项
     */
    constructor(scene, options = {}) {
        super(scene, options);
        
        // 设置系统需要的组件
        this.requiredComponents = ['RenderComponent'];
        
        // 设置系统优先级（渲染系统通常优先级较低）
        this.priority = 100;
        
        // 渲染配置
        this.renderConfig = {
            enableLOD: options.enableLOD !== false,           // 启用LOD
            enableFrustumCulling: options.enableFrustumCulling !== false, // 启用视锥体剔除
            enableOcclusionCulling: options.enableOcclusionCulling || false, // 启用遮挡剔除
            enableBatching: options.enableBatching || false,   // 启用批处理
            maxDrawCalls: options.maxDrawCalls || 1000,       // 最大绘制调用数
            lodDistances: options.lodDistances || [50, 100, 200], // LOD距离
            cullingMargin: options.cullingMargin || 10        // 剔除边距
        };
        
        // 渲染状态
        this.renderStats = {
            visibleEntities: 0,
            culledEntities: 0,
            totalDrawCalls: 0,
            totalTriangles: 0,
            totalVertices: 0,
            renderTime: 0,
            lastFrameTime: 0
        };
        
        // 渲染组
        this.renderGroups = new Map(); // groupId -> entities[]
        
        // LOD管理
        this.lodEntities = new Set();
        
        // 批处理管理
        this.batchGroups = new Map(); // materialId -> entities[]
        
        // 相机引用
        this.camera = null;
        
        console.log('渲染系统已创建');
    }
    
    /**
     * 系统初始化
     */
    onInitialize() {
        // 获取活动相机
        this.camera = this.scene.activeCamera;
        
        if (!this.camera) {
            console.warn('渲染系统：未找到活动相机');
        }
        
        // 监听相机变化
        this.scene.onActiveCameraChanged.add((scene) => {
            this.camera = scene.activeCamera;
            console.log('渲染系统：活动相机已变更');
        });
        
        // 设置渲染优化
        this.setupRenderOptimizations();
        
        console.log('渲染系统初始化完成');
    }
    
    /**
     * 设置渲染优化
     * @private
     */
    setupRenderOptimizations() {
        // 启用视锥体剔除
        if (this.renderConfig.enableFrustumCulling) {
            this.scene.setRenderingAutoClearDepthStencil(0, false);
        }
        
        // 启用遮挡剔除
        if (this.renderConfig.enableOcclusionCulling) {
            this.scene.occlusionQueryEnabled = true;
        }
        
        // 设置渲染组
        this.scene.setRenderingOrder(
            0,  // opaque sorting function
            0,  // alpha test sorting function  
            0   // transparent sorting function
        );
    }
    
    /**
     * 实体添加到系统时的处理
     * @param {Entity} entity - 被添加的实体
     */
    onEntityAdded(entity) {
        super.onEntityAdded(entity);
        
        const renderComponent = entity.getComponent('RenderComponent');
        if (!renderComponent) return;
        
        // 添加到渲染组
        const groupId = renderComponent.renderConfig.renderingGroupId || 0;
        this.addToRenderGroup(entity, groupId);
        
        // 检查是否需要LOD
        if (this.renderConfig.enableLOD && renderComponent.lodConfig.enabled) {
            this.lodEntities.add(entity);
        }
        
        // 检查是否可以批处理
        if (this.renderConfig.enableBatching && renderComponent.material) {
            const materialId = renderComponent.material.id;
            this.addToBatchGroup(entity, materialId);
        }
        
        console.log(`实体已添加到渲染系统: ${entity.id}`);
    }
    
    /**
     * 实体从系统移除时的处理
     * @param {Entity} entity - 被移除的实体
     */
    onEntityRemoved(entity) {
        super.onEntityRemoved(entity);
        
        // 从渲染组移除
        this.removeFromRenderGroups(entity);
        
        // 从LOD管理移除
        this.lodEntities.delete(entity);
        
        // 从批处理组移除
        this.removeFromBatchGroups(entity);
        
        console.log(`实体已从渲染系统移除: ${entity.id}`);
    }
    
    /**
     * 系统更新
     * @param {number} deltaTime - 时间间隔
     */
    onUpdate(deltaTime) {
        const startTime = performance.now();
        
        // 重置统计
        this.resetRenderStats();
        
        // 更新相机引用
        if (!this.camera) {
            this.camera = this.scene.activeCamera;
        }
        
        if (!this.camera) {
            return; // 没有相机，跳过渲染更新
        }
        
        // 执行视锥体剔除
        if (this.renderConfig.enableFrustumCulling) {
            this.performFrustumCulling();
        }
        
        // 更新LOD
        if (this.renderConfig.enableLOD) {
            this.updateLOD();
        }
        
        // 更新渲染组件
        this.entities.forEach(entity => {
            this.updateEntity(entity, deltaTime);
        });
        
        // 执行批处理
        if (this.renderConfig.enableBatching) {
            this.performBatching();
        }
        
        // 更新统计
        this.updateRenderStats(startTime);
    }
    
    /**
     * 更新单个实体
     * @param {Entity} entity - 实体
     * @param {number} deltaTime - 时间间隔
     */
    updateEntity(entity, deltaTime) {
        const renderComponent = entity.getComponent('RenderComponent');
        if (!renderComponent || !renderComponent.isLoaded) return;
        
        // 更新渲染组件
        renderComponent.update(deltaTime);
        
        // 检查可见性
        if (renderComponent.mesh && renderComponent.isVisible) {
            this.renderStats.visibleEntities++;
            
            // 更新统计信息
            const boundingInfo = renderComponent.getBoundingInfo();
            if (boundingInfo) {
                // 这里可以添加更详细的统计信息收集
            }
        } else {
            this.renderStats.culledEntities++;
        }
    }
    
    /**
     * 执行视锥体剔除
     * @private
     */
    performFrustumCulling() {
        if (!this.camera) return;
        
        const frustumPlanes = this.camera.getFrustumPlanes();
        
        this.entities.forEach(entity => {
            const renderComponent = entity.getComponent('RenderComponent');
            if (!renderComponent || !renderComponent.mesh) return;
            
            const boundingInfo = renderComponent.mesh.getBoundingInfo();
            const isInFrustum = this.camera.isInFrustum(boundingInfo.boundingBox);
            
            // 设置网格可见性
            renderComponent.mesh.setEnabled(isInFrustum);
            
            if (!isInFrustum) {
                this.renderStats.culledEntities++;
            }
        });
    }
    
    /**
     * 更新LOD
     * @private
     */
    updateLOD() {
        if (!this.camera) return;
        
        this.lodEntities.forEach(entity => {
            const renderComponent = entity.getComponent('RenderComponent');
            if (!renderComponent || !renderComponent.mesh) return;
            
            const transformComponent = entity.getComponent('TransformComponent');
            if (!transformComponent) return;
            
            // 计算到相机的距离
            const distance = this.camera.position.subtract(transformComponent.position).length();
            
            // 根据距离选择LOD级别
            const lodLevel = this.calculateLODLevel(distance);
            
            // 应用LOD
            this.applyLOD(renderComponent, lodLevel);
        });
    }
    
    /**
     * 计算LOD级别
     * @private
     * @param {number} distance - 距离
     * @returns {number} LOD级别
     */
    calculateLODLevel(distance) {
        const distances = this.renderConfig.lodDistances;
        
        for (let i = 0; i < distances.length; i++) {
            if (distance < distances[i]) {
                return i;
            }
        }
        
        return distances.length; // 最低LOD级别
    }
    
    /**
     * 应用LOD
     * @private
     * @param {RenderComponent} renderComponent - 渲染组件
     * @param {number} lodLevel - LOD级别
     */
    applyLOD(renderComponent, lodLevel) {
        const lodConfig = renderComponent.lodConfig;
        
        if (!lodConfig.enabled || !lodConfig.meshes[lodLevel]) {
            return;
        }
        
        // 隐藏当前网格
        if (renderComponent.mesh) {
            renderComponent.mesh.setEnabled(false);
        }
        
        // 显示LOD网格
        const lodMesh = lodConfig.meshes[lodLevel];
        if (lodMesh) {
            lodMesh.setEnabled(true);
            
            // 同步变换
            const transformComponent = renderComponent.entity.getComponent('TransformComponent');
            if (transformComponent) {
                lodMesh.position.copyFrom(transformComponent.position);
                lodMesh.rotationQuaternion = transformComponent.rotation.clone();
                lodMesh.scaling.copyFrom(transformComponent.scale);
            }
        }
    }
    
    /**
     * 执行批处理
     * @private
     */
    performBatching() {
        this.batchGroups.forEach((entities, materialId) => {
            if (entities.length < 2) return; // 至少需要2个实体才能批处理
            
            // 这里可以实现具体的批处理逻辑
            // 例如合并相同材质的网格
            this.createBatch(entities, materialId);
        });
    }
    
    /**
     * 创建批处理
     * @private
     * @param {Array<Entity>} entities - 实体数组
     * @param {string} materialId - 材质ID
     */
    createBatch(entities, materialId) {
        // 简化的批处理实现
        // 实际实现需要更复杂的网格合并逻辑
        
        const visibleEntities = entities.filter(entity => {
            const renderComponent = entity.getComponent('RenderComponent');
            return renderComponent && renderComponent.isVisible && renderComponent.mesh;
        });
        
        if (visibleEntities.length < 2) return;
        
        // 这里可以使用Babylon.js的MergeMeshes功能
        // 或者使用实例化渲染
        console.log(`批处理 ${visibleEntities.length} 个实体，材质ID: ${materialId}`);
    }
    
    /**
     * 添加到渲染组
     * @private
     * @param {Entity} entity - 实体
     * @param {number} groupId - 组ID
     */
    addToRenderGroup(entity, groupId) {
        if (!this.renderGroups.has(groupId)) {
            this.renderGroups.set(groupId, []);
        }
        
        this.renderGroups.get(groupId).push(entity);
    }
    
    /**
     * 添加到批处理组
     * @private
     * @param {Entity} entity - 实体
     * @param {string} materialId - 材质ID
     */
    addToBatchGroup(entity, materialId) {
        if (!this.batchGroups.has(materialId)) {
            this.batchGroups.set(materialId, []);
        }
        
        this.batchGroups.get(materialId).push(entity);
    }
    
    /**
     * 从渲染组移除
     * @private
     * @param {Entity} entity - 实体
     */
    removeFromRenderGroups(entity) {
        this.renderGroups.forEach((entities, groupId) => {
            const index = entities.indexOf(entity);
            if (index !== -1) {
                entities.splice(index, 1);
                
                // 如果组为空，删除组
                if (entities.length === 0) {
                    this.renderGroups.delete(groupId);
                }
            }
        });
    }
    
    /**
     * 从批处理组移除
     * @private
     * @param {Entity} entity - 实体
     */
    removeFromBatchGroups(entity) {
        this.batchGroups.forEach((entities, materialId) => {
            const index = entities.indexOf(entity);
            if (index !== -1) {
                entities.splice(index, 1);
                
                // 如果组为空，删除组
                if (entities.length === 0) {
                    this.batchGroups.delete(materialId);
                }
            }
        });
    }
    
    /**
     * 重置渲染统计
     * @private
     */
    resetRenderStats() {
        this.renderStats.visibleEntities = 0;
        this.renderStats.culledEntities = 0;
        this.renderStats.totalDrawCalls = 0;
        this.renderStats.totalTriangles = 0;
        this.renderStats.totalVertices = 0;
    }
    
    /**
     * 更新渲染统计
     * @private
     * @param {number} startTime - 开始时间
     */
    updateRenderStats(startTime) {
        this.renderStats.renderTime = performance.now() - startTime;
        this.renderStats.lastFrameTime = performance.now();
        
        // 计算总绘制调用数
        this.renderStats.totalDrawCalls = this.renderStats.visibleEntities;
        
        // 计算总三角形和顶点数
        let totalTriangles = 0;
        let totalVertices = 0;
        
        this.entities.forEach(entity => {
            const renderComponent = entity.getComponent('RenderComponent');
            if (renderComponent && renderComponent.isVisible) {
                totalTriangles += renderComponent.renderStats.triangleCount;
                totalVertices += renderComponent.renderStats.vertexCount;
            }
        });
        
        this.renderStats.totalTriangles = totalTriangles;
        this.renderStats.totalVertices = totalVertices;
    }
    
    /**
     * 设置渲染配置
     * @param {Object} config - 配置对象
     */
    setRenderConfig(config) {
        this.renderConfig = { ...this.renderConfig, ...config };
        
        // 重新设置渲染优化
        this.setupRenderOptimizations();
        
        this.emit('configChanged', { system: this, config: this.renderConfig });
    }
    
    /**
     * 获取渲染统计
     * @returns {Object} 渲染统计信息
     */
    getRenderStats() {
        return {
            ...this.renderStats,
            entityCount: this.entities.size,
            renderGroupCount: this.renderGroups.size,
            batchGroupCount: this.batchGroups.size,
            lodEntityCount: this.lodEntities.size
        };
    }
    
    /**
     * 强制更新所有渲染组件
     */
    forceUpdate() {
        this.entities.forEach(entity => {
            const renderComponent = entity.getComponent('RenderComponent');
            if (renderComponent) {
                renderComponent.needsUpdate = true;
            }
        });
    }
    
    /**
     * 系统清理
     */
    onDispose() {
        // 清理渲染组
        this.renderGroups.clear();
        
        // 清理批处理组
        this.batchGroups.clear();
        
        // 清理LOD实体
        this.lodEntities.clear();
        
        // 清理相机引用
        this.camera = null;
        
        console.log('渲染系统已清理');
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            renderConfig: this.renderConfig,
            renderStats: this.getRenderStats(),
            hasCamera: !!this.camera,
            cameraType: this.camera?.constructor.name
        };
    }
}

export default RenderSystem;
