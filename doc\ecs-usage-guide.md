# ECS使用指南

## 快速开始

### 1. 基础设置

首先，在你的项目中设置ECS系统：

```javascript
import { setupECS } from './src/ecs/index.js';

// 假设你已经有一个Babylon.js场景
const scene = new BABYLON.Scene(engine);

// 设置ECS系统
const ecs = setupECS(scene, {
    manager: {
        maxEntities: 5000,
        enablePerformanceMonitoring: true
    },
    render: {
        enableLOD: true,
        enableFrustumCulling: true
    },
    physics: {
        gravity: { x: 0, y: -9.81, z: 0 },
        enableDebugDraw: false
    }
});

// 启动ECS系统
ecs.start();
```

### 2. 创建你的第一个实体

```javascript
// 使用实体构建器创建一个简单的立方体
const cube = ecs.createEntity('my-cube')
    .at(0, 1, 0)                    // 位置
    .withRender({                   // 渲染组件
        meshType: 'box',
        diffuseColor: '#ff0000'
    })
    .withPhysics({                  // 物理组件
        type: 'box',
        mass: 1.0
    })
    .build();
```

### 3. 游戏循环

```javascript
function gameLoop() {
    // 更新ECS系统
    ecs.update();
    
    // 渲染场景
    scene.render();
    
    requestAnimationFrame(gameLoop);
}

gameLoop();
```

## 实体管理

### 使用预设创建实体

ECS系统提供了多种预设，可以快速创建常见类型的实体：

```javascript
import { ENTITY_PRESETS } from './src/ecs/index.js';

// 创建玩家
const player = ecs.createEntity('player')
    .fromPreset(ENTITY_PRESETS.PLAYER)
    .at(0, 0, 0)
    .withColor('#0066ff')
    .asLocalOwned('player1')        // 设置为本地拥有
    .build();

// 创建NPC
const npc = ecs.createEntity('guard')
    .fromPreset(ENTITY_PRESETS.NPC)
    .at(5, 0, 0)
    .withColor('#00ff00')
    .build();

// 创建静态环境物体
const wall = ecs.createEntity('wall')
    .fromPreset(ENTITY_PRESETS.STATIC)
    .at(0, 0, 10)
    .scaledBy(10, 3, 1)
    .withColor('#888888')
    .build();
```

### 动态修改实体

```javascript
// 获取实体
const player = ecs.manager.getEntity('player_id');

// 修改位置
const transform = player.getComponent('TransformComponent');
transform.setPosition(10, 0, 5);

// 修改颜色
const render = player.getComponent('RenderComponent');
render.setColor('diffuse', new BABYLON.Color3(1, 0, 0));

// 应用力
const physics = player.getComponent('PhysicsComponent');
physics.applyForce(new BABYLON.Vector3(0, 100, 0));
```

### 实体查询

```javascript
// 查询所有具有物理组件的实体
const physicsEntities = ecs.manager.queryEntities('PhysicsComponent');

// 查询所有玩家（具有特定标签）
const players = ecs.manager.getAllEntities().filter(entity => 
    entity.hasTag('player')
);

// 查询具有多个组件的实体
const renderablePhysicsEntities = ecs.manager.queryEntities(
    'RenderComponent', 
    'PhysicsComponent'
);
```

## 组件开发

### 创建自定义组件

```javascript
import { Component } from './src/ecs/index.js';

class HealthComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        
        // 设置依赖组件
        this.dependencies = ['TransformComponent'];
        
        // 初始化属性
        this.maxHealth = options.maxHealth || 100;
        this.currentHealth = this.maxHealth;
        this.regenerationRate = options.regenerationRate || 0;
        this.isInvulnerable = false;
    }
    
    onInitialize() {
        // 组件初始化逻辑
        console.log(`健康组件已初始化: ${this.entity.id}`);
    }
    
    onUpdate(deltaTime) {
        // 每帧更新逻辑
        if (this.regenerationRate > 0 && this.currentHealth < this.maxHealth) {
            this.heal(this.regenerationRate * deltaTime);
        }
    }
    
    takeDamage(amount) {
        if (this.isInvulnerable) return;
        
        this.currentHealth = Math.max(0, this.currentHealth - amount);
        
        // 触发事件
        this.emit('healthChanged', {
            current: this.currentHealth,
            max: this.maxHealth,
            percentage: this.currentHealth / this.maxHealth
        });
        
        if (this.currentHealth === 0) {
            this.emit('death', { entity: this.entity });
        }
    }
    
    heal(amount) {
        const oldHealth = this.currentHealth;
        this.currentHealth = Math.min(this.maxHealth, this.currentHealth + amount);
        
        if (this.currentHealth !== oldHealth) {
            this.emit('healthChanged', {
                current: this.currentHealth,
                max: this.maxHealth,
                percentage: this.currentHealth / this.maxHealth
            });
        }
    }
    
    // 序列化支持
    onSerialize() {
        return {
            maxHealth: this.maxHealth,
            currentHealth: this.currentHealth,
            regenerationRate: this.regenerationRate,
            isInvulnerable: this.isInvulnerable
        };
    }
    
    onDeserialize(data) {
        this.maxHealth = data.maxHealth;
        this.currentHealth = data.currentHealth;
        this.regenerationRate = data.regenerationRate;
        this.isInvulnerable = data.isInvulnerable;
    }
}

// 注册组件
ecs.manager.registerComponent(HealthComponent, 'HealthComponent');
```

### 使用自定义组件

```javascript
// 创建带有健康组件的实体
const enemy = ecs.createEntity('enemy')
    .fromPreset(ENTITY_PRESETS.NPC)
    .at(0, 0, 5)
    .withComponent('HealthComponent', {
        maxHealth: 150,
        regenerationRate: 2
    })
    .build();

// 监听健康变化事件
const healthComponent = enemy.getComponent('HealthComponent');
healthComponent.on('healthChanged', (data) => {
    console.log(`健康值变化: ${data.current}/${data.max} (${data.percentage * 100}%)`);
});

healthComponent.on('death', () => {
    console.log('实体死亡');
    enemy.destroy();
});

// 造成伤害
healthComponent.takeDamage(50);
```

## 系统开发

### 创建自定义系统

```javascript
import { System } from './src/ecs/index.js';

class HealthSystem extends System {
    constructor(scene, options = {}) {
        super(scene, options);
        
        // 设置系统需要的组件
        this.requiredComponents = ['HealthComponent'];
        
        // 设置系统优先级
        this.priority = 50;
        
        // 系统配置
        this.damageOverTimeRate = options.damageOverTimeRate || 0;
    }
    
    onInitialize() {
        console.log('健康系统已初始化');
        
        // 监听全局事件
        this.scene.onBeforeRenderObservable.add(() => {
            // 渲染前的处理
        });
    }
    
    updateEntity(entity, deltaTime) {
        const health = entity.getComponent('HealthComponent');
        
        // 检查死亡条件
        if (health.currentHealth <= 0) {
            this.handleEntityDeath(entity);
            return;
        }
        
        // 处理持续伤害
        if (this.damageOverTimeRate > 0) {
            health.takeDamage(this.damageOverTimeRate * deltaTime);
        }
        
        // 检查特殊状态
        if (entity.hasTag('poisoned')) {
            health.takeDamage(10 * deltaTime);
        }
        
        if (entity.hasTag('blessed')) {
            health.heal(5 * deltaTime);
        }
    }
    
    handleEntityDeath(entity) {
        // 播放死亡动画
        const animation = entity.getComponent('AnimationComponent');
        if (animation) {
            animation.play('death', {
                onAnimationEnd: () => {
                    entity.destroy();
                }
            });
        } else {
            entity.destroy();
        }
        
        // 掉落物品
        this.spawnLoot(entity);
        
        // 更新统计
        this.emit('entityDied', { entity, system: this });
    }
    
    spawnLoot(entity) {
        const transform = entity.getComponent('TransformComponent');
        if (!transform) return;
        
        // 在实体位置创建掉落物
        const loot = this.scene.ecsManager.createEntity('loot')
            .at(transform.position.x, transform.position.y + 1, transform.position.z)
            .withRender({
                meshType: 'sphere',
                diffuseColor: '#ffff00'
            })
            .withPhysics({
                type: 'sphere',
                mass: 0.1
            })
            .withTags('loot', 'collectible')
            .build();
    }
}

// 添加系统到ECS管理器
const healthSystem = new HealthSystem(scene, {
    damageOverTimeRate: 1
});
ecs.manager.addSystem(healthSystem);
```

## 高级功能

### 实体模板和工厂

```javascript
import { componentFactory } from './src/ecs/index.js';

// 创建自定义预设
componentFactory.addPreset('boss', {
    components: [
        { type: 'TransformComponent', options: {} },
        { type: 'RenderComponent', options: { 
            meshType: 'sphere',
            diffuseColor: '#ff0000'
        }},
        { type: 'PhysicsComponent', options: { 
            type: 'sphere', 
            mass: 100 
        }},
        { type: 'HealthComponent', options: { 
            maxHealth: 1000,
            regenerationRate: 10
        }},
        { type: 'AnimationComponent', options: { 
            autoPlay: 'idle' 
        }}
    ]
});

// 使用自定义预设
const boss = ecs.createEntity('boss')
    .fromPreset('boss')
    .at(0, 0, 20)
    .scaledBy(3)
    .withTags('enemy', 'boss')
    .build();
```

### 性能优化

```javascript
// 设置系统更新频率
const aiSystem = new AISystem(scene);
aiSystem.setUpdateInterval(100); // 每100ms更新一次

// 使用对象池
class BulletPool {
    constructor(ecs, size = 100) {
        this.ecs = ecs;
        this.pool = [];
        this.active = new Set();
        
        // 预创建子弹
        for (let i = 0; i < size; i++) {
            const bullet = this.createBullet();
            bullet.setActive(false);
            this.pool.push(bullet);
        }
    }
    
    getBullet() {
        let bullet = this.pool.pop();
        if (!bullet) {
            bullet = this.createBullet();
        }
        
        bullet.setActive(true);
        this.active.add(bullet);
        return bullet;
    }
    
    returnBullet(bullet) {
        if (this.active.has(bullet)) {
            bullet.setActive(false);
            this.active.delete(bullet);
            this.pool.push(bullet);
        }
    }
    
    createBullet() {
        return this.ecs.createEntity('bullet')
            .withRender({ meshType: 'sphere' })
            .withPhysics({ type: 'sphere', mass: 0.1 })
            .build();
    }
}
```

### 事件系统

```javascript
// 全局事件监听
ecs.manager.on('entityCreated', (data) => {
    console.log(`实体已创建: ${data.entity.id}`);
});

// 组件事件监听
const player = ecs.manager.getEntity('player');
const health = player.getComponent('HealthComponent');

health.on('healthChanged', (data) => {
    // 更新UI
    updateHealthBar(data.percentage);
});

// 系统事件监听
const combatSystem = ecs.manager.getSystem('CombatSystem');
combatSystem.on('damageDealt', (data) => {
    // 显示伤害数字
    showDamageNumber(data.amount, data.position);
});
```

### 调试和监控

```javascript
// 获取性能统计
const stats = ecs.manager.getPerformanceStats();
console.log(`FPS: ${stats.fps}, 实体数量: ${stats.entityCount}`);

// 获取系统信息
const renderSystem = ecs.systems.render;
const renderStats = renderSystem.getRenderStats();
console.log(`可见实体: ${renderStats.visibleEntities}, 绘制调用: ${renderStats.totalDrawCalls}`);

// 调试特定实体
const entity = ecs.manager.getEntity('player');
console.log(entity.getDebugInfo());

// 启用物理调试绘制
const physicsSystem = ecs.systems.physics;
physicsSystem.setDebugDrawEnabled(true);
```

## 最佳实践

### 1. 组件设计原则

- **单一职责**：每个组件只负责一个明确的功能
- **数据驱动**：组件主要存储数据，逻辑放在系统中
- **避免依赖**：尽量减少组件之间的直接依赖

### 2. 系统设计原则

- **批量处理**：尽可能批量处理实体以提高性能
- **无状态**：系统应该是无状态的，状态存储在组件中
- **错误隔离**：一个系统的错误不应该影响其他系统

### 3. 性能优化建议

- **合理使用查询缓存**：频繁查询的结果应该被缓存
- **控制更新频率**：不是所有系统都需要每帧更新
- **使用对象池**：对于频繁创建/销毁的对象使用对象池
- **批量操作**：批量处理相似的操作

### 4. 内存管理

- **及时清理**：确保实体和组件被正确销毁
- **避免循环引用**：注意组件和实体之间的引用关系
- **监控内存使用**：定期检查内存使用情况

## 故障排除

### 常见问题

1. **组件未找到**
   ```javascript
   // 错误：组件未注册
   const component = entity.getComponent('MyComponent'); // null
   
   // 解决：确保组件已注册
   ecs.manager.registerComponent(MyComponent);
   ```

2. **系统不更新实体**
   ```javascript
   // 检查系统的组件要求
   console.log(system.requiredComponents);
   
   // 检查实体是否有必需的组件
   console.log(entity.hasAllComponents(...system.requiredComponents));
   ```

3. **性能问题**
   ```javascript
   // 检查系统性能
   const stats = system.getPerformanceStats();
   console.log(`系统 ${system.constructor.name} 平均更新时间: ${stats.averageUpdateTime}ms`);
   ```

### 调试技巧

- 使用浏览器开发者工具的性能分析器
- 启用ECS系统的性能监控
- 使用console.log记录关键事件
- 检查实体和组件的调试信息

通过遵循这些指南和最佳实践，你可以有效地使用ECS系统来构建复杂而高性能的游戏应用。
