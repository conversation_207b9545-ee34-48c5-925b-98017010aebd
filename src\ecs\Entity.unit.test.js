// src/ecs/Entity.unit.test.js
// Entity类的单元测试

import Entity from './Entity.js';
import Component from './Component.js';

// 创建测试用的组件类
class TestComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.testValue = options.testValue || 'default';
    }
    
    onInitialize() {
        this.initialized = true;
    }
    
    onUpdate(deltaTime) {
        this.lastUpdateTime = deltaTime;
    }
}

class AnotherTestComponent extends Component {
    constructor(entity, options = {}) {
        super(entity, options);
        this.anotherValue = options.anotherValue || 42;
    }
}

describe('Entity', () => {
    let entity;
    
    beforeEach(() => {
        entity = new Entity('test-entity', 'Test Entity');
    });
    
    afterEach(() => {
        if (entity && !entity.isDestroyed) {
            entity.destroy();
        }
    });
    
    describe('构造函数', () => {
        test('应该正确创建实体', () => {
            expect(entity.id).toBe('test-entity');
            expect(entity.name).toBe('Test Entity');
            expect(entity.isActive).toBe(true);
            expect(entity.isDestroyed).toBe(false);
            expect(entity.components).toBeInstanceOf(Map);
            expect(entity.tags).toBeInstanceOf(Set);
            expect(entity.metadata).toBeInstanceOf(Map);
        });
        
        test('应该设置默认名称', () => {
            const entityWithoutName = new Entity('test-id');
            expect(entityWithoutName.name).toBe('test-id');
            entityWithoutName.destroy();
        });
        
        test('应该设置创建时间', () => {
            expect(entity.createdAt).toBeGreaterThan(0);
            expect(entity.createdAt).toBeLessThanOrEqual(Date.now());
        });
    });
    
    describe('组件管理', () => {
        let testComponent;
        
        beforeEach(() => {
            testComponent = new TestComponent(entity, { testValue: 'test' });
        });
        
        test('应该能添加组件', () => {
            const result = entity.addComponent(testComponent);
            
            expect(result).toBe(entity); // 支持链式调用
            expect(entity.hasComponent('TestComponent')).toBe(true);
            expect(entity.getComponent('TestComponent')).toBe(testComponent);
            expect(testComponent.entity).toBe(entity);
        });
        
        test('应该能移除组件', () => {
            entity.addComponent(testComponent);
            
            const result = entity.removeComponent('TestComponent');
            
            expect(result).toBe(true);
            expect(entity.hasComponent('TestComponent')).toBe(false);
            expect(entity.getComponent('TestComponent')).toBeNull();
            expect(testComponent.entity).toBeNull();
        });
        
        test('应该能通过类引用获取组件', () => {
            entity.addComponent(testComponent);
            
            expect(entity.hasComponent(TestComponent)).toBe(true);
            expect(entity.getComponent(TestComponent)).toBe(testComponent);
        });
        
        test('应该防止添加重复组件类型', () => {
            entity.addComponent(testComponent);
            
            const duplicateComponent = new TestComponent(entity);
            const result = entity.addComponent(duplicateComponent);
            
            expect(result).toBe(entity); // 仍然返回实体
            expect(entity.getComponent('TestComponent')).toBe(testComponent); // 保持原组件
        });
        
        test('应该能检查多个组件', () => {
            const anotherComponent = new AnotherTestComponent(entity);
            
            entity.addComponent(testComponent);
            entity.addComponent(anotherComponent);
            
            expect(entity.hasAllComponents('TestComponent', 'AnotherTestComponent')).toBe(true);
            expect(entity.hasAllComponents('TestComponent', 'NonExistentComponent')).toBe(false);
            expect(entity.hasAnyComponent('TestComponent', 'NonExistentComponent')).toBe(true);
            expect(entity.hasAnyComponent('NonExistentComponent1', 'NonExistentComponent2')).toBe(false);
        });
        
        test('应该能获取所有组件', () => {
            const anotherComponent = new AnotherTestComponent(entity);
            
            entity.addComponent(testComponent);
            entity.addComponent(anotherComponent);
            
            const allComponents = entity.getAllComponents();
            const componentTypes = entity.getComponentTypes();
            
            expect(allComponents).toHaveLength(2);
            expect(allComponents).toContain(testComponent);
            expect(allComponents).toContain(anotherComponent);
            expect(componentTypes).toContain('TestComponent');
            expect(componentTypes).toContain('AnotherTestComponent');
        });
        
        test('移除不存在的组件应该返回false', () => {
            const result = entity.removeComponent('NonExistentComponent');
            expect(result).toBe(false);
        });
    });
    
    describe('标签管理', () => {
        test('应该能添加和检查标签', () => {
            const result = entity.addTag('player');
            
            expect(result).toBe(entity); // 支持链式调用
            expect(entity.hasTag('player')).toBe(true);
            expect(entity.hasTag('enemy')).toBe(false);
        });
        
        test('应该能移除标签', () => {
            entity.addTag('player');
            
            const result = entity.removeTag('player');
            
            expect(result).toBe(true);
            expect(entity.hasTag('player')).toBe(false);
        });
        
        test('应该能获取所有标签', () => {
            entity.addTag('player');
            entity.addTag('human');
            entity.addTag('friendly');
            
            const tags = entity.getTags();
            
            expect(tags).toHaveLength(3);
            expect(tags).toContain('player');
            expect(tags).toContain('human');
            expect(tags).toContain('friendly');
        });
        
        test('移除不存在的标签应该返回false', () => {
            const result = entity.removeTag('nonexistent');
            expect(result).toBe(false);
        });
    });
    
    describe('元数据管理', () => {
        test('应该能设置和获取元数据', () => {
            const result = entity.setMetadata('health', 100);
            
            expect(result).toBe(entity); // 支持链式调用
            expect(entity.getMetadata('health')).toBe(100);
            expect(entity.getMetadata('nonexistent')).toBeUndefined();
        });
        
        test('应该能存储不同类型的元数据', () => {
            entity.setMetadata('string', 'value');
            entity.setMetadata('number', 42);
            entity.setMetadata('boolean', true);
            entity.setMetadata('object', { key: 'value' });
            entity.setMetadata('array', [1, 2, 3]);
            
            expect(entity.getMetadata('string')).toBe('value');
            expect(entity.getMetadata('number')).toBe(42);
            expect(entity.getMetadata('boolean')).toBe(true);
            expect(entity.getMetadata('object')).toEqual({ key: 'value' });
            expect(entity.getMetadata('array')).toEqual([1, 2, 3]);
        });
    });
    
    describe('激活状态管理', () => {
        test('应该能设置激活状态', () => {
            entity.setActive(false);
            
            expect(entity.isActive).toBe(false);
        });
        
        test('设置激活状态应该通知组件', () => {
            const testComponent = new TestComponent(entity);
            const mockOnActiveChanged = jest.fn();
            testComponent.onActiveChanged = mockOnActiveChanged;
            
            entity.addComponent(testComponent);
            entity.setActive(false);
            
            expect(mockOnActiveChanged).toHaveBeenCalledWith(false);
        });
        
        test('设置相同的激活状态不应该触发事件', () => {
            const mockEmit = jest.spyOn(entity, 'emit');
            
            entity.setActive(true); // 已经是true
            
            expect(mockEmit).not.toHaveBeenCalledWith('activeChanged', expect.any(Object));
        });
    });
    
    describe('父子关系', () => {
        let parentEntity;
        let childEntity;
        
        beforeEach(() => {
            parentEntity = new Entity('parent');
            childEntity = new Entity('child');
        });
        
        afterEach(() => {
            if (parentEntity && !parentEntity.isDestroyed) {
                parentEntity.destroy();
            }
            if (childEntity && !childEntity.isDestroyed) {
                childEntity.destroy();
            }
        });
        
        test('应该能添加子实体', () => {
            parentEntity.addChild(childEntity);
            
            expect(childEntity.parent).toBe(parentEntity);
            expect(parentEntity.children.has(childEntity)).toBe(true);
        });
        
        test('应该能移除子实体', () => {
            parentEntity.addChild(childEntity);
            parentEntity.removeChild(childEntity);
            
            expect(childEntity.parent).toBeNull();
            expect(parentEntity.children.has(childEntity)).toBe(false);
        });
        
        test('添加已有父实体的子实体应该先移除旧关系', () => {
            const oldParent = new Entity('old-parent');
            
            oldParent.addChild(childEntity);
            parentEntity.addChild(childEntity);
            
            expect(childEntity.parent).toBe(parentEntity);
            expect(oldParent.children.has(childEntity)).toBe(false);
            expect(parentEntity.children.has(childEntity)).toBe(true);
            
            oldParent.destroy();
        });
    });
    
    describe('实体销毁', () => {
        test('应该能销毁实体', () => {
            const testComponent = new TestComponent(entity);
            entity.addComponent(testComponent);
            entity.addTag('test');
            entity.setMetadata('key', 'value');
            
            entity.destroy();
            
            expect(entity.isDestroyed).toBe(true);
            expect(entity.isActive).toBe(false);
            expect(entity.components.size).toBe(0);
            expect(entity.tags.size).toBe(0);
            expect(entity.metadata.size).toBe(0);
        });
        
        test('销毁实体应该销毁所有子实体', () => {
            const childEntity = new Entity('child');
            entity.addChild(childEntity);
            
            entity.destroy();
            
            expect(childEntity.isDestroyed).toBe(true);
        });
        
        test('重复销毁应该被忽略', () => {
            const mockEmit = jest.spyOn(entity, 'emit');
            
            entity.destroy();
            entity.destroy(); // 第二次销毁
            
            // 只应该触发一次销毁事件
            expect(mockEmit).toHaveBeenCalledWith('destroyed', expect.any(Object));
            expect(mockEmit).toHaveBeenCalledTimes(3); // beforeDestroy, destroyed, 和其他可能的事件
        });
        
        test('销毁后的操作应该被忽略', () => {
            entity.destroy();
            
            const testComponent = new TestComponent(entity);
            entity.addComponent(testComponent);
            entity.setActive(false);
            
            expect(entity.components.size).toBe(0);
            expect(entity.isActive).toBe(false); // 销毁时已设置为false
        });
    });
    
    describe('调试信息', () => {
        test('应该提供调试信息', () => {
            const testComponent = new TestComponent(entity);
            entity.addComponent(testComponent);
            entity.addTag('test');
            entity.setMetadata('key', 'value');
            
            const debugInfo = entity.getDebugInfo();
            
            expect(debugInfo).toHaveProperty('id', 'test-entity');
            expect(debugInfo).toHaveProperty('name', 'Test Entity');
            expect(debugInfo).toHaveProperty('isActive', true);
            expect(debugInfo).toHaveProperty('isDestroyed', false);
            expect(debugInfo).toHaveProperty('componentCount', 1);
            expect(debugInfo).toHaveProperty('componentTypes');
            expect(debugInfo).toHaveProperty('tags');
            expect(debugInfo).toHaveProperty('childrenCount', 0);
            expect(debugInfo).toHaveProperty('hasParent', false);
            expect(debugInfo.componentTypes).toContain('TestComponent');
            expect(debugInfo.tags).toContain('test');
        });
        
        test('toString应该返回有意义的字符串', () => {
            const str = entity.toString();
            expect(str).toContain('test-entity');
            expect(str).toContain('Test Entity');
            expect(str).toContain('components: 0');
        });
    });
    
    describe('事件系统', () => {
        test('应该能触发和监听事件', () => {
            const mockCallback = jest.fn();
            
            entity.on('test-event', mockCallback);
            entity.emit('test-event', { data: 'test' });
            
            expect(mockCallback).toHaveBeenCalledWith({ data: 'test' });
        });
        
        test('组件添加应该触发事件', () => {
            const mockCallback = jest.fn();
            const testComponent = new TestComponent(entity);
            
            entity.on('componentAdded', mockCallback);
            entity.addComponent(testComponent);
            
            expect(mockCallback).toHaveBeenCalledWith({
                entity,
                component: testComponent,
                componentType: 'TestComponent'
            });
        });
        
        test('组件移除应该触发事件', () => {
            const mockCallback = jest.fn();
            const testComponent = new TestComponent(entity);
            
            entity.addComponent(testComponent);
            entity.on('componentRemoved', mockCallback);
            entity.removeComponent('TestComponent');
            
            expect(mockCallback).toHaveBeenCalledWith({
                entity,
                component: testComponent,
                componentType: 'TestComponent'
            });
        });
    });
});
