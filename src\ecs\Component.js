// src/ecs/Component.js
// 实体组件系统 - 组件基类
// 组件是实体的数据和行为的载体，定义了实体的各种属性和功能

import { EventEmitter } from '../utils/eventEmitter.js';

/**
 * 组件基类 - ECS架构中的组件基类
 * 组件包含数据和相关的行为逻辑
 * 所有具体的组件都应该继承自这个基类
 */
export class Component extends EventEmitter {
    /**
     * 构造函数
     * @param {Entity} entity - 所属的实体实例
     * @param {Object} [options={}] - 组件初始化选项
     */
    constructor(entity = null, options = {}) {
        super();
        
        // 所属的实体引用
        this.entity = entity;
        
        // 组件是否启用
        this.enabled = true;
        
        // 组件创建时间
        this.createdAt = Date.now();
        
        // 组件的配置选项
        this.options = { ...options };
        
        // 组件状态
        this.isInitialized = false;
        this.isDestroyed = false;
        
        // 更新相关
        this.needsUpdate = false;
        this.updatePriority = 0; // 更新优先级，数值越小优先级越高
        
        // 依赖的组件类型列表
        this.dependencies = [];
        
        // 元数据存储
        this.metadata = new Map();
        
        console.log(`组件已创建: ${this.constructor.name}`);
    }
    
    /**
     * 组件被附加到实体时调用
     * 子类可以重写此方法来执行初始化逻辑
     */
    onAttached() {
        if (this.isDestroyed) {
            console.warn(`尝试附加已销毁的组件: ${this.constructor.name}`);
            return;
        }
        
        // 检查依赖组件
        if (!this.checkDependencies()) {
            console.error(`组件 ${this.constructor.name} 的依赖组件不满足`);
            return;
        }
        
        // 执行初始化
        this.initialize();
        
        this.emit('attached', { component: this, entity: this.entity });
        
        console.log(`组件已附加: ${this.constructor.name} -> ${this.entity?.id}`);
    }
    
    /**
     * 组件从实体分离时调用
     * 子类可以重写此方法来执行清理逻辑
     */
    onDetached() {
        this.cleanup();
        
        this.emit('detached', { component: this, entity: this.entity });
        
        console.log(`组件已分离: ${this.constructor.name} <- ${this.entity?.id}`);
    }
    
    /**
     * 实体激活状态变化时调用
     * @param {boolean} isActive - 新的激活状态
     */
    onActiveChanged(isActive) {
        this.emit('activeChanged', { component: this, isActive });
        
        if (isActive) {
            this.onEnabled();
        } else {
            this.onDisabled();
        }
    }
    
    /**
     * 组件初始化方法
     * 子类应该重写此方法来执行具体的初始化逻辑
     */
    initialize() {
        if (this.isInitialized) {
            console.warn(`组件已经初始化: ${this.constructor.name}`);
            return;
        }
        
        this.isInitialized = true;
        
        // 子类可以重写此方法
        this.onInitialize();
        
        this.emit('initialized', { component: this });
        
        console.log(`组件初始化完成: ${this.constructor.name}`);
    }
    
    /**
     * 子类重写的初始化方法
     * 在这里执行具体的初始化逻辑
     */
    onInitialize() {
        // 子类实现
    }
    
    /**
     * 组件更新方法
     * 在每帧被系统调用
     * @param {number} deltaTime - 距离上一帧的时间间隔（秒）
     */
    update(deltaTime) {
        if (!this.enabled || this.isDestroyed || !this.entity?.isActive) {
            return;
        }
        
        // 子类可以重写此方法
        this.onUpdate(deltaTime);
        
        // 重置更新标记
        this.needsUpdate = false;
    }
    
    /**
     * 子类重写的更新方法
     * @param {number} deltaTime - 距离上一帧的时间间隔（秒）
     */
    onUpdate(deltaTime) {
        // 子类实现
    }
    
    /**
     * 组件启用时调用
     */
    onEnabled() {
        // 子类可以重写此方法
    }
    
    /**
     * 组件禁用时调用
     */
    onDisabled() {
        // 子类可以重写此方法
    }
    
    /**
     * 设置组件的启用状态
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        if (this.enabled !== enabled) {
            this.enabled = enabled;
            
            this.emit('enabledChanged', { component: this, enabled });
            
            if (enabled) {
                this.onEnabled();
            } else {
                this.onDisabled();
            }
            
            console.log(`组件 ${this.constructor.name} 启用状态已变更: ${enabled}`);
        }
    }
    
    /**
     * 标记组件需要更新
     */
    markForUpdate() {
        this.needsUpdate = true;
    }
    
    /**
     * 检查依赖组件是否满足
     * @returns {boolean} 依赖是否满足
     */
    checkDependencies() {
        if (!this.entity) {
            return false;
        }
        
        return this.dependencies.every(depType => {
            const hasComponent = this.entity.hasComponent(depType);
            if (!hasComponent) {
                console.warn(`组件 ${this.constructor.name} 缺少依赖组件: ${depType}`);
            }
            return hasComponent;
        });
    }
    
    /**
     * 获取依赖的组件
     * @param {string|Function} componentType - 组件类型
     * @returns {Component|null} 依赖的组件实例
     */
    getDependency(componentType) {
        if (!this.entity) {
            return null;
        }
        
        return this.entity.getComponent(componentType);
    }
    
    /**
     * 设置元数据
     * @param {string} key - 键
     * @param {*} value - 值
     * @returns {Component} 返回自身，支持链式调用
     */
    setMetadata(key, value) {
        this.metadata.set(key, value);
        return this;
    }
    
    /**
     * 获取元数据
     * @param {string} key - 键
     * @returns {*} 元数据值
     */
    getMetadata(key) {
        return this.metadata.get(key);
    }
    
    /**
     * 复制组件配置到另一个组件
     * @param {Component} targetComponent - 目标组件
     */
    copyTo(targetComponent) {
        if (targetComponent.constructor !== this.constructor) {
            console.warn(`尝试复制到不同类型的组件: ${this.constructor.name} -> ${targetComponent.constructor.name}`);
            return;
        }
        
        // 复制配置选项
        targetComponent.options = { ...this.options };
        
        // 复制元数据
        this.metadata.forEach((value, key) => {
            targetComponent.setMetadata(key, value);
        });
        
        // 复制启用状态
        targetComponent.setEnabled(this.enabled);
        
        // 子类可以重写此方法来复制特定数据
        this.onCopyTo(targetComponent);
    }
    
    /**
     * 子类重写的复制方法
     * @param {Component} targetComponent - 目标组件
     */
    onCopyTo(targetComponent) {
        // 子类实现
    }
    
    /**
     * 序列化组件数据
     * @returns {Object} 序列化后的数据
     */
    serialize() {
        const data = {
            type: this.constructor.name,
            enabled: this.enabled,
            options: this.options,
            metadata: Object.fromEntries(this.metadata),
            updatePriority: this.updatePriority
        };
        
        // 子类可以重写此方法来添加特定数据
        const customData = this.onSerialize();
        if (customData) {
            data.customData = customData;
        }
        
        return data;
    }
    
    /**
     * 子类重写的序列化方法
     * @returns {Object} 自定义序列化数据
     */
    onSerialize() {
        // 子类实现
        return null;
    }
    
    /**
     * 反序列化组件数据
     * @param {Object} data - 序列化的数据
     */
    deserialize(data) {
        if (data.enabled !== undefined) {
            this.setEnabled(data.enabled);
        }
        
        if (data.options) {
            this.options = { ...data.options };
        }
        
        if (data.metadata) {
            Object.entries(data.metadata).forEach(([key, value]) => {
                this.setMetadata(key, value);
            });
        }
        
        if (data.updatePriority !== undefined) {
            this.updatePriority = data.updatePriority;
        }
        
        // 子类可以重写此方法来处理特定数据
        if (data.customData) {
            this.onDeserialize(data.customData);
        }
    }
    
    /**
     * 子类重写的反序列化方法
     * @param {Object} customData - 自定义数据
     */
    onDeserialize(customData) {
        // 子类实现
    }
    
    /**
     * 清理组件资源
     */
    cleanup() {
        if (this.isDestroyed) {
            return;
        }
        
        this.isDestroyed = true;
        this.enabled = false;
        
        // 子类可以重写此方法来执行特定的清理逻辑
        this.onCleanup();
        
        // 清理元数据
        this.metadata.clear();
        
        // 清理事件监听器
        this.removeAllListeners();
        
        this.emit('destroyed', { component: this });
        
        console.log(`组件清理完成: ${this.constructor.name}`);
    }
    
    /**
     * 子类重写的清理方法
     */
    onCleanup() {
        // 子类实现
    }
    
    /**
     * 获取组件的调试信息
     * @returns {Object} 调试信息对象
     */
    getDebugInfo() {
        return {
            type: this.constructor.name,
            enabled: this.enabled,
            isInitialized: this.isInitialized,
            isDestroyed: this.isDestroyed,
            createdAt: this.createdAt,
            entityId: this.entity?.id,
            updatePriority: this.updatePriority,
            needsUpdate: this.needsUpdate,
            dependencies: this.dependencies,
            options: this.options
        };
    }
    
    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `${this.constructor.name}(entity: ${this.entity?.id}, enabled: ${this.enabled})`;
    }
}

export default Component;
