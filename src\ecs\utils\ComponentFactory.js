// src/ecs/utils/ComponentFactory.js
// 组件工厂 - 提供便捷的组件创建和配置方法

import { Vector3, Color3, Quaternion } from '@babylonjs/core';
import componentRegistry from '../ComponentRegistry.js';

// 导入所有组件类
import TransformComponent from '../components/TransformComponent.js';
import RenderComponent from '../components/RenderComponent.js';
import PhysicsComponent from '../components/PhysicsComponent.js';
import AnimationComponent from '../components/AnimationComponent.js';
import NetworkComponent from '../components/NetworkComponent.js';

/**
 * 组件工厂类
 * 提供便捷的组件创建方法和预设配置
 */
export class ComponentFactory {
    constructor() {
        // 注册所有组件类型
        this.registerAllComponents();
        
        // 预设配置
        this.presets = new Map();
        this.initializePresets();
        
        console.log('组件工厂已创建');
    }
    
    /**
     * 注册所有组件类型
     * @private
     */
    registerAllComponents() {
        const components = [
            {
                componentClass: TransformComponent,
                metadata: {
                    description: '变换组件 - 管理实体的位置、旋转和缩放',
                    group: 'core',
                    dependencies: []
                }
            },
            {
                componentClass: RenderComponent,
                metadata: {
                    description: '渲染组件 - 管理实体的视觉表现',
                    group: 'rendering',
                    dependencies: ['TransformComponent']
                }
            },
            {
                componentClass: PhysicsComponent,
                metadata: {
                    description: '物理组件 - 管理实体的物理属性',
                    group: 'physics',
                    dependencies: ['TransformComponent']
                }
            },
            {
                componentClass: AnimationComponent,
                metadata: {
                    description: '动画组件 - 管理实体的动画播放',
                    group: 'animation',
                    dependencies: []
                }
            },
            {
                componentClass: NetworkComponent,
                metadata: {
                    description: '网络组件 - 管理实体的网络同步',
                    group: 'network',
                    dependencies: ['TransformComponent']
                }
            }
        ];
        
        componentRegistry.registerBatch(components);
    }
    
    /**
     * 初始化预设配置
     * @private
     */
    initializePresets() {
        // 基础实体预设
        this.presets.set('basic', {
            components: [
                { type: 'TransformComponent', options: {} }
            ]
        });
        
        // 可见实体预设
        this.presets.set('visible', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'box' } }
            ]
        });
        
        // 物理实体预设
        this.presets.set('physics', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'box' } },
                { type: 'PhysicsComponent', options: { type: 'box', mass: 1.0 } }
            ]
        });
        
        // 玩家预设
        this.presets.set('player', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'capsule' } },
                { type: 'PhysicsComponent', options: { type: 'capsule', mass: 70.0 } },
                { type: 'AnimationComponent', options: { autoPlay: 'idle' } },
                { type: 'NetworkComponent', options: { syncPosition: true, syncRotation: true } }
            ]
        });
        
        // NPC预设
        this.presets.set('npc', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'capsule' } },
                { type: 'PhysicsComponent', options: { type: 'capsule', mass: 60.0 } },
                { type: 'AnimationComponent', options: { autoPlay: 'idle' } }
            ]
        });
        
        // 静态物体预设
        this.presets.set('static', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'box' } },
                { type: 'PhysicsComponent', options: { type: 'box', mass: 0, isStatic: true } }
            ]
        });
        
        // 触发器预设
        this.presets.set('trigger', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'PhysicsComponent', options: { type: 'box', mass: 0, isTrigger: true } }
            ]
        });
        
        // 装饰物预设
        this.presets.set('decoration', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'sphere' } }
            ]
        });
        
        // 粒子效果预设
        this.presets.set('particle', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'plane', visible: false } },
                { type: 'AnimationComponent', options: {} }
            ]
        });
        
        // 网络同步物体预设
        this.presets.set('networked', {
            components: [
                { type: 'TransformComponent', options: {} },
                { type: 'RenderComponent', options: { meshType: 'box' } },
                { type: 'NetworkComponent', options: { syncPosition: true } }
            ]
        });
    }
    
    /**
     * 创建组件
     * @param {string} componentType - 组件类型名
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {Component|null} 组件实例
     */
    createComponent(componentType, entity, options = {}) {
        return componentRegistry.createComponent(componentType, entity, options);
    }
    
    /**
     * 创建变换组件
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {TransformComponent} 变换组件
     */
    createTransformComponent(entity, options = {}) {
        const defaultOptions = {
            position: Vector3.Zero(),
            rotation: Quaternion.Identity(),
            scale: Vector3.One()
        };
        
        return this.createComponent('TransformComponent', entity, { ...defaultOptions, ...options });
    }
    
    /**
     * 创建渲染组件
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {RenderComponent} 渲染组件
     */
    createRenderComponent(entity, options = {}) {
        const defaultOptions = {
            meshType: 'box',
            materialType: 'standard',
            visible: true,
            castShadows: true,
            receiveShadows: true
        };
        
        return this.createComponent('RenderComponent', entity, { ...defaultOptions, ...options });
    }
    
    /**
     * 创建物理组件
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {PhysicsComponent} 物理组件
     */
    createPhysicsComponent(entity, options = {}) {
        const defaultOptions = {
            type: 'box',
            mass: 1.0,
            restitution: 0.3,
            friction: 0.5,
            isStatic: false,
            isTrigger: false
        };
        
        return this.createComponent('PhysicsComponent', entity, { ...defaultOptions, ...options });
    }
    
    /**
     * 创建动画组件
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {AnimationComponent} 动画组件
     */
    createAnimationComponent(entity, options = {}) {
        const defaultOptions = {
            defaultSpeed: 1.0,
            defaultLoop: false,
            enableBlending: true,
            autoPlay: null
        };
        
        return this.createComponent('AnimationComponent', entity, { ...defaultOptions, ...options });
    }
    
    /**
     * 创建网络组件
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     * @returns {NetworkComponent} 网络组件
     */
    createNetworkComponent(entity, options = {}) {
        const defaultOptions = {
            syncPosition: true,
            syncRotation: true,
            syncScale: false,
            updateRate: 20,
            interpolation: true,
            isLocalOwned: false
        };
        
        return this.createComponent('NetworkComponent', entity, { ...defaultOptions, ...options });
    }
    
    /**
     * 使用预设创建组件集合
     * @param {string} presetName - 预设名称
     * @param {Entity} entity - 所属实体
     * @param {Object} [overrides={}] - 覆盖选项
     * @returns {Array<Component>} 组件数组
     */
    createFromPreset(presetName, entity, overrides = {}) {
        const preset = this.presets.get(presetName);
        if (!preset) {
            console.error(`未知的预设: ${presetName}`);
            return [];
        }
        
        const components = [];
        
        preset.components.forEach(componentConfig => {
            const { type, options } = componentConfig;
            
            // 应用覆盖选项
            const finalOptions = overrides[type] ? 
                { ...options, ...overrides[type] } : 
                options;
            
            const component = this.createComponent(type, entity, finalOptions);
            if (component) {
                components.push(component);
                entity.addComponent(component);
            }
        });
        
        console.log(`使用预设 ${presetName} 创建了 ${components.length} 个组件`);
        
        return components;
    }
    
    /**
     * 创建玩家实体组件
     * @param {Entity} entity - 实体
     * @param {Object} [options={}] - 选项
     * @returns {Array<Component>} 组件数组
     */
    createPlayerComponents(entity, options = {}) {
        const overrides = {
            TransformComponent: {
                position: options.position || Vector3.Zero()
            },
            RenderComponent: {
                meshType: 'capsule',
                diffuseColor: options.color || new Color3(0.5, 0.8, 1.0)
            },
            PhysicsComponent: {
                type: 'capsule',
                mass: options.mass || 70.0,
                height: options.height || 1.8,
                radius: options.radius || 0.3
            },
            NetworkComponent: {
                isLocalOwned: options.isLocalPlayer || false,
                ownerId: options.playerId || null
            }
        };
        
        return this.createFromPreset('player', entity, overrides);
    }
    
    /**
     * 创建静态环境物体组件
     * @param {Entity} entity - 实体
     * @param {Object} [options={}] - 选项
     * @returns {Array<Component>} 组件数组
     */
    createStaticObjectComponents(entity, options = {}) {
        const overrides = {
            TransformComponent: {
                position: options.position || Vector3.Zero(),
                scale: options.scale || Vector3.One()
            },
            RenderComponent: {
                meshType: options.meshType || 'box',
                diffuseColor: options.color || new Color3(0.7, 0.7, 0.7)
            },
            PhysicsComponent: {
                type: options.physicsType || options.meshType || 'box',
                isStatic: true
            }
        };
        
        return this.createFromPreset('static', entity, overrides);
    }
    
    /**
     * 创建可交互物体组件
     * @param {Entity} entity - 实体
     * @param {Object} [options={}] - 选项
     * @returns {Array<Component>} 组件数组
     */
    createInteractableComponents(entity, options = {}) {
        const overrides = {
            TransformComponent: {
                position: options.position || Vector3.Zero()
            },
            RenderComponent: {
                meshType: options.meshType || 'box',
                diffuseColor: options.color || new Color3(1.0, 0.8, 0.2)
            },
            PhysicsComponent: {
                type: options.physicsType || options.meshType || 'box',
                mass: options.mass || 1.0,
                isTrigger: options.isTrigger || false
            },
            AnimationComponent: {
                autoPlay: options.idleAnimation || null
            }
        };
        
        return this.createFromPreset('physics', entity, overrides);
    }
    
    /**
     * 添加自定义预设
     * @param {string} name - 预设名称
     * @param {Object} preset - 预设配置
     */
    addPreset(name, preset) {
        this.presets.set(name, preset);
        console.log(`自定义预设已添加: ${name}`);
    }
    
    /**
     * 获取预设
     * @param {string} name - 预设名称
     * @returns {Object|null} 预设配置
     */
    getPreset(name) {
        return this.presets.get(name) || null;
    }
    
    /**
     * 获取所有预设名称
     * @returns {Array<string>} 预设名称数组
     */
    getPresetNames() {
        return Array.from(this.presets.keys());
    }
    
    /**
     * 移除预设
     * @param {string} name - 预设名称
     * @returns {boolean} 是否成功移除
     */
    removePreset(name) {
        return this.presets.delete(name);
    }
    
    /**
     * 克隆组件
     * @param {Component} sourceComponent - 源组件
     * @param {Entity} targetEntity - 目标实体
     * @returns {Component|null} 克隆的组件
     */
    cloneComponent(sourceComponent, targetEntity) {
        const componentType = sourceComponent.constructor.name;
        
        // 序列化源组件
        const serializedData = sourceComponent.serialize();
        
        // 创建新组件
        const newComponent = this.createComponent(componentType, targetEntity, sourceComponent.options);
        
        if (newComponent) {
            // 反序列化数据到新组件
            newComponent.deserialize(serializedData);
        }
        
        return newComponent;
    }
    
    /**
     * 批量创建组件
     * @param {Array} componentConfigs - 组件配置数组
     * @param {Entity} entity - 实体
     * @returns {Array<Component>} 组件数组
     */
    createBatch(componentConfigs, entity) {
        const components = [];
        
        componentConfigs.forEach(config => {
            const { type, options } = config;
            const component = this.createComponent(type, entity, options);
            
            if (component) {
                components.push(component);
                entity.addComponent(component);
            }
        });
        
        return components;
    }
    
    /**
     * 验证组件配置
     * @param {Object} config - 组件配置
     * @returns {Object} 验证结果
     */
    validateComponentConfig(config) {
        const result = {
            valid: true,
            errors: [],
            warnings: []
        };
        
        if (!config.type) {
            result.valid = false;
            result.errors.push('缺少组件类型');
        } else if (!componentRegistry.isRegistered(config.type)) {
            result.valid = false;
            result.errors.push(`未注册的组件类型: ${config.type}`);
        }
        
        // 检查依赖
        const metadata = componentRegistry.getComponentMetadata(config.type);
        if (metadata && metadata.dependencies) {
            metadata.dependencies.forEach(dep => {
                if (!componentRegistry.isRegistered(dep)) {
                    result.warnings.push(`依赖组件未注册: ${dep}`);
                }
            });
        }
        
        return result;
    }
    
    /**
     * 获取组件工厂统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            registeredComponents: componentRegistry.getAllComponentNames().length,
            availablePresets: this.presets.size,
            componentGroups: componentRegistry.getAllGroups().length
        };
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            stats: this.getStats(),
            presets: Array.from(this.presets.keys()),
            registeredComponents: componentRegistry.getAllComponentNames(),
            componentGroups: componentRegistry.getAllGroups()
        };
    }
}

// 创建全局单例实例
export const componentFactory = new ComponentFactory();

export default componentFactory;
