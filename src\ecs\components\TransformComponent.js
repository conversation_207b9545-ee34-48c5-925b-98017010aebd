// src/ecs/components/TransformComponent.js
// 变换组件 - 管理实体的位置、旋转和缩放

import { Vector3, Quaternion, Matrix } from '@babylonjs/core';
import Component from '../Component.js';

/**
 * 变换组件类
 * 管理实体在3D空间中的位置、旋转和缩放
 * 这是最基础和最重要的组件之一
 */
export class TransformComponent extends Component {
    /**
     * 构造函数
     * @param {Entity} entity - 所属实体
     * @param {Object} [options={}] - 组件选项
     */
    constructor(entity = null, options = {}) {
        super(entity, options);
        
        // 位置向量
        this.position = options.position ? 
            new Vector3(options.position.x, options.position.y, options.position.z) : 
            Vector3.Zero();
        
        // 旋转四元数
        this.rotation = options.rotation ? 
            new Quaternion(options.rotation.x, options.rotation.y, options.rotation.z, options.rotation.w) :
            Quaternion.Identity();
        
        // 缩放向量
        this.scale = options.scale ? 
            new Vector3(options.scale.x, options.scale.y, options.scale.z) :
            Vector3.One();
        
        // 欧拉角（用于方便的旋转操作）
        this._eulerAngles = Vector3.Zero();
        this._eulerDirty = false;
        
        // 变换矩阵缓存
        this._worldMatrix = Matrix.Identity();
        this._localMatrix = Matrix.Identity();
        this._matrixDirty = true;
        
        // 父子关系
        this.parent = null;
        this.children = new Set();
        
        // 变换变化标记
        this.hasChanged = false;
        this.lastChangeTime = 0;
        
        // 约束设置
        this.constraints = {
            positionMin: null,
            positionMax: null,
            scaleMin: null,
            scaleMax: null,
            lockPosition: { x: false, y: false, z: false },
            lockRotation: { x: false, y: false, z: false },
            lockScale: { x: false, y: false, z: false }
        };
        
        console.log(`变换组件已创建: ${this.entity?.id}`);
    }
    
    /**
     * 组件初始化
     */
    onInitialize() {
        // 标记矩阵需要更新
        this.markMatrixDirty();
        
        // 如果有初始变换，应用约束
        this.applyConstraints();
    }
    
    /**
     * 设置位置
     * @param {number|Vector3} x - X坐标或Vector3对象
     * @param {number} [y] - Y坐标
     * @param {number} [z] - Z坐标
     */
    setPosition(x, y, z) {
        if (x instanceof Vector3) {
            this.position.copyFrom(x);
        } else {
            this.position.set(x, y, z);
        }
        
        this.applyPositionConstraints();
        this.markChanged();
    }
    
    /**
     * 获取位置的副本
     * @returns {Vector3} 位置向量
     */
    getPosition() {
        return this.position.clone();
    }
    
    /**
     * 移动位置（相对移动）
     * @param {number|Vector3} x - X偏移或Vector3偏移
     * @param {number} [y] - Y偏移
     * @param {number} [z] - Z偏移
     */
    translate(x, y, z) {
        if (x instanceof Vector3) {
            this.position.addInPlace(x);
        } else {
            this.position.addInPlace(new Vector3(x, y, z));
        }
        
        this.applyPositionConstraints();
        this.markChanged();
    }
    
    /**
     * 设置旋转（使用四元数）
     * @param {Quaternion} quaternion - 旋转四元数
     */
    setRotation(quaternion) {
        this.rotation.copyFrom(quaternion);
        this._eulerDirty = true;
        this.markChanged();
    }
    
    /**
     * 设置旋转（使用欧拉角）
     * @param {number|Vector3} x - X轴旋转角度（弧度）或Vector3欧拉角
     * @param {number} [y] - Y轴旋转角度（弧度）
     * @param {number} [z] - Z轴旋转角度（弧度）
     */
    setRotationFromEuler(x, y, z) {
        if (x instanceof Vector3) {
            this._eulerAngles.copyFrom(x);
        } else {
            this._eulerAngles.set(x, y, z);
        }
        
        // 应用旋转约束
        this.applyRotationConstraints();
        
        // 从欧拉角创建四元数
        this.rotation = Quaternion.FromEulerAngles(
            this._eulerAngles.x,
            this._eulerAngles.y,
            this._eulerAngles.z
        );
        
        this._eulerDirty = false;
        this.markChanged();
    }
    
    /**
     * 获取欧拉角
     * @returns {Vector3} 欧拉角向量
     */
    getEulerAngles() {
        if (this._eulerDirty) {
            this._eulerAngles = this.rotation.toEulerAngles();
            this._eulerDirty = false;
        }
        return this._eulerAngles.clone();
    }
    
    /**
     * 旋转（相对旋转）
     * @param {number|Vector3} x - X轴旋转角度（弧度）或Vector3欧拉角
     * @param {number} [y] - Y轴旋转角度（弧度）
     * @param {number} [z] - Z轴旋转角度（弧度）
     */
    rotate(x, y, z) {
        let deltaRotation;
        
        if (x instanceof Vector3) {
            deltaRotation = Quaternion.FromEulerAngles(x.x, x.y, x.z);
        } else {
            deltaRotation = Quaternion.FromEulerAngles(x, y, z);
        }
        
        this.rotation.multiplyInPlace(deltaRotation);
        this._eulerDirty = true;
        this.markChanged();
    }
    
    /**
     * 设置缩放
     * @param {number|Vector3} x - X轴缩放或Vector3缩放
     * @param {number} [y] - Y轴缩放
     * @param {number} [z] - Z轴缩放
     */
    setScale(x, y, z) {
        if (x instanceof Vector3) {
            this.scale.copyFrom(x);
        } else if (typeof x === 'number' && y === undefined && z === undefined) {
            // 统一缩放
            this.scale.set(x, x, x);
        } else {
            this.scale.set(x, y, z);
        }
        
        this.applyScaleConstraints();
        this.markChanged();
    }
    
    /**
     * 获取缩放的副本
     * @returns {Vector3} 缩放向量
     */
    getScale() {
        return this.scale.clone();
    }
    
    /**
     * 缩放（相对缩放）
     * @param {number|Vector3} x - X轴缩放因子或Vector3缩放因子
     * @param {number} [y] - Y轴缩放因子
     * @param {number} [z] - Z轴缩放因子
     */
    scaleBy(x, y, z) {
        if (x instanceof Vector3) {
            this.scale.multiplyInPlace(x);
        } else if (typeof x === 'number' && y === undefined && z === undefined) {
            // 统一缩放
            this.scale.scaleInPlace(x);
        } else {
            this.scale.multiplyInPlace(new Vector3(x, y, z));
        }
        
        this.applyScaleConstraints();
        this.markChanged();
    }
    
    /**
     * 获取本地变换矩阵
     * @returns {Matrix} 本地变换矩阵
     */
    getLocalMatrix() {
        if (this._matrixDirty) {
            Matrix.ComposeToRef(this.scale, this.rotation, this.position, this._localMatrix);
            this._matrixDirty = false;
        }
        return this._localMatrix.clone();
    }
    
    /**
     * 获取世界变换矩阵
     * @returns {Matrix} 世界变换矩阵
     */
    getWorldMatrix() {
        const localMatrix = this.getLocalMatrix();
        
        if (this.parent) {
            const parentWorldMatrix = this.parent.getWorldMatrix();
            this._worldMatrix = localMatrix.multiply(parentWorldMatrix);
        } else {
            this._worldMatrix = localMatrix;
        }
        
        return this._worldMatrix.clone();
    }
    
    /**
     * 获取世界位置
     * @returns {Vector3} 世界位置
     */
    getWorldPosition() {
        const worldMatrix = this.getWorldMatrix();
        return worldMatrix.getTranslation();
    }
    
    /**
     * 获取世界旋转
     * @returns {Quaternion} 世界旋转
     */
    getWorldRotation() {
        const worldMatrix = this.getWorldMatrix();
        const scale = new Vector3();
        const rotation = new Quaternion();
        const translation = new Vector3();
        
        worldMatrix.decompose(scale, rotation, translation);
        return rotation;
    }
    
    /**
     * 获取前方向量（基于当前旋转）
     * @returns {Vector3} 前方向量
     */
    getForward() {
        return Vector3.TransformNormal(Vector3.Forward(), Matrix.RotationQuaternion(this.rotation));
    }
    
    /**
     * 获取右方向量（基于当前旋转）
     * @returns {Vector3} 右方向量
     */
    getRight() {
        return Vector3.TransformNormal(Vector3.Right(), Matrix.RotationQuaternion(this.rotation));
    }
    
    /**
     * 获取上方向量（基于当前旋转）
     * @returns {Vector3} 上方向量
     */
    getUp() {
        return Vector3.TransformNormal(Vector3.Up(), Matrix.RotationQuaternion(this.rotation));
    }
    
    /**
     * 朝向指定位置
     * @param {Vector3} target - 目标位置
     * @param {Vector3} [up=Vector3.Up()] - 上方向
     */
    lookAt(target, up = Vector3.Up()) {
        const direction = target.subtract(this.position).normalize();
        const rotationMatrix = Matrix.LookAtLH(Vector3.Zero(), direction, up);
        this.rotation = Quaternion.FromRotationMatrix(rotationMatrix);
        this._eulerDirty = true;
        this.markChanged();
    }
    
    /**
     * 设置父变换组件
     * @param {TransformComponent} parent - 父变换组件
     */
    setParent(parent) {
        // 移除旧的父子关系
        if (this.parent) {
            this.parent.children.delete(this);
        }
        
        // 设置新的父子关系
        this.parent = parent;
        if (parent) {
            parent.children.add(this);
        }
        
        this.markChanged();
    }
    
    /**
     * 添加子变换组件
     * @param {TransformComponent} child - 子变换组件
     */
    addChild(child) {
        child.setParent(this);
    }
    
    /**
     * 移除子变换组件
     * @param {TransformComponent} child - 子变换组件
     */
    removeChild(child) {
        if (this.children.has(child)) {
            child.setParent(null);
        }
    }
    
    /**
     * 设置位置约束
     * @param {Vector3} [min] - 最小位置
     * @param {Vector3} [max] - 最大位置
     */
    setPositionConstraints(min, max) {
        this.constraints.positionMin = min ? min.clone() : null;
        this.constraints.positionMax = max ? max.clone() : null;
        this.applyPositionConstraints();
    }
    
    /**
     * 设置缩放约束
     * @param {Vector3} [min] - 最小缩放
     * @param {Vector3} [max] - 最大缩放
     */
    setScaleConstraints(min, max) {
        this.constraints.scaleMin = min ? min.clone() : null;
        this.constraints.scaleMax = max ? max.clone() : null;
        this.applyScaleConstraints();
    }
    
    /**
     * 应用位置约束
     * @private
     */
    applyPositionConstraints() {
        const { positionMin, positionMax, lockPosition } = this.constraints;
        
        if (positionMin) {
            this.position.x = Math.max(this.position.x, positionMin.x);
            this.position.y = Math.max(this.position.y, positionMin.y);
            this.position.z = Math.max(this.position.z, positionMin.z);
        }
        
        if (positionMax) {
            this.position.x = Math.min(this.position.x, positionMax.x);
            this.position.y = Math.min(this.position.y, positionMax.y);
            this.position.z = Math.min(this.position.z, positionMax.z);
        }
        
        // 应用位置锁定
        if (lockPosition.x) this.position.x = 0;
        if (lockPosition.y) this.position.y = 0;
        if (lockPosition.z) this.position.z = 0;
    }
    
    /**
     * 应用旋转约束
     * @private
     */
    applyRotationConstraints() {
        const { lockRotation } = this.constraints;
        
        // 应用旋转锁定
        if (lockRotation.x) this._eulerAngles.x = 0;
        if (lockRotation.y) this._eulerAngles.y = 0;
        if (lockRotation.z) this._eulerAngles.z = 0;
    }
    
    /**
     * 应用缩放约束
     * @private
     */
    applyScaleConstraints() {
        const { scaleMin, scaleMax, lockScale } = this.constraints;
        
        if (scaleMin) {
            this.scale.x = Math.max(this.scale.x, scaleMin.x);
            this.scale.y = Math.max(this.scale.y, scaleMin.y);
            this.scale.z = Math.max(this.scale.z, scaleMin.z);
        }
        
        if (scaleMax) {
            this.scale.x = Math.min(this.scale.x, scaleMax.x);
            this.scale.y = Math.min(this.scale.y, scaleMax.y);
            this.scale.z = Math.min(this.scale.z, scaleMax.z);
        }
        
        // 应用缩放锁定
        if (lockScale.x) this.scale.x = 1;
        if (lockScale.y) this.scale.y = 1;
        if (lockScale.z) this.scale.z = 1;
    }
    
    /**
     * 应用所有约束
     */
    applyConstraints() {
        this.applyPositionConstraints();
        this.applyRotationConstraints();
        this.applyScaleConstraints();
    }
    
    /**
     * 标记变换已改变
     * @private
     */
    markChanged() {
        this.hasChanged = true;
        this.lastChangeTime = performance.now();
        this.markMatrixDirty();
        
        // 通知子组件
        this.children.forEach(child => {
            child.markChanged();
        });
        
        // 触发变换改变事件
        this.emit('transformChanged', {
            component: this,
            position: this.position,
            rotation: this.rotation,
            scale: this.scale
        });
    }
    
    /**
     * 标记矩阵需要更新
     * @private
     */
    markMatrixDirty() {
        this._matrixDirty = true;
    }
    
    /**
     * 重置变化标记
     */
    resetChangeFlag() {
        this.hasChanged = false;
    }
    
    /**
     * 序列化组件数据
     * @returns {Object} 序列化数据
     */
    onSerialize() {
        return {
            position: {
                x: this.position.x,
                y: this.position.y,
                z: this.position.z
            },
            rotation: {
                x: this.rotation.x,
                y: this.rotation.y,
                z: this.rotation.z,
                w: this.rotation.w
            },
            scale: {
                x: this.scale.x,
                y: this.scale.y,
                z: this.scale.z
            },
            constraints: this.constraints
        };
    }
    
    /**
     * 反序列化组件数据
     * @param {Object} data - 序列化数据
     */
    onDeserialize(data) {
        if (data.position) {
            this.setPosition(data.position.x, data.position.y, data.position.z);
        }
        
        if (data.rotation) {
            this.setRotation(new Quaternion(
                data.rotation.x,
                data.rotation.y,
                data.rotation.z,
                data.rotation.w
            ));
        }
        
        if (data.scale) {
            this.setScale(data.scale.x, data.scale.y, data.scale.z);
        }
        
        if (data.constraints) {
            this.constraints = { ...this.constraints, ...data.constraints };
            this.applyConstraints();
        }
    }
    
    /**
     * 组件清理
     */
    onCleanup() {
        // 清理父子关系
        if (this.parent) {
            this.parent.children.delete(this);
        }
        
        this.children.forEach(child => {
            child.parent = null;
        });
        this.children.clear();
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            position: this.position.asArray(),
            rotation: this.rotation.asArray(),
            scale: this.scale.asArray(),
            eulerAngles: this.getEulerAngles().asArray(),
            hasParent: !!this.parent,
            childrenCount: this.children.size,
            hasChanged: this.hasChanged,
            lastChangeTime: this.lastChangeTime,
            constraints: this.constraints
        };
    }
}

export default TransformComponent;
