// src/ecs/System.js
// 实体组件系统 - 系统基类
// 系统负责处理具有特定组件组合的实体，实现游戏逻辑

import { EventEmitter } from '../utils/eventEmitter.js';

/**
 * 系统基类 - ECS架构中的系统基类
 * 系统处理具有特定组件组合的实体，实现游戏的核心逻辑
 * 所有具体的系统都应该继承自这个基类
 */
export class System extends EventEmitter {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 系统初始化选项
     */
    constructor(scene, options = {}) {
        super();
        
        // Babylon.js场景引用
        this.scene = scene;
        
        // 系统配置选项
        this.options = { ...options };
        
        // 系统是否启用
        this.enabled = true;
        
        // 系统状态
        this.isInitialized = false;
        this.isDestroyed = false;
        
        // 系统优先级，数值越小优先级越高
        this.priority = 0;
        
        // 系统关注的实体列表
        this.entities = new Set();
        
        // 系统需要的组件类型列表（子类应该设置这个）
        this.requiredComponents = [];
        
        // 系统排除的组件类型列表（拥有这些组件的实体不会被处理）
        this.excludedComponents = [];
        
        // 更新频率控制
        this.updateInterval = 0; // 0表示每帧更新
        this.lastUpdateTime = 0;
        
        // 性能统计
        this.performanceStats = {
            updateCount: 0,
            totalUpdateTime: 0,
            averageUpdateTime: 0,
            lastUpdateTime: 0,
            entityCount: 0
        };
        
        // 创建时间
        this.createdAt = Date.now();
        
        console.log(`系统已创建: ${this.constructor.name}`);
    }
    
    /**
     * 系统初始化方法
     * 子类应该重写此方法来执行具体的初始化逻辑
     */
    initialize() {
        if (this.isInitialized) {
            console.warn(`系统已经初始化: ${this.constructor.name}`);
            return;
        }
        
        this.isInitialized = true;
        
        // 子类可以重写此方法
        this.onInitialize();
        
        this.emit('initialized', { system: this });
        
        console.log(`系统初始化完成: ${this.constructor.name}`);
    }
    
    /**
     * 子类重写的初始化方法
     */
    onInitialize() {
        // 子类实现
    }
    
    /**
     * 系统更新方法
     * 在每帧被ECS管理器调用
     * @param {number} deltaTime - 距离上一帧的时间间隔（秒）
     * @param {Set<Entity>} allEntities - 所有实体的集合
     */
    update(deltaTime, allEntities) {
        if (!this.enabled || this.isDestroyed) {
            return;
        }
        
        // 检查更新频率
        const currentTime = performance.now();
        if (this.updateInterval > 0 && 
            currentTime - this.lastUpdateTime < this.updateInterval) {
            return;
        }
        
        // 记录性能统计开始时间
        const startTime = performance.now();
        
        // 更新关注的实体列表
        this.updateEntityList(allEntities);
        
        // 执行系统逻辑
        this.onUpdate(deltaTime);
        
        // 更新性能统计
        const endTime = performance.now();
        const updateTime = endTime - startTime;
        
        this.performanceStats.updateCount++;
        this.performanceStats.totalUpdateTime += updateTime;
        this.performanceStats.averageUpdateTime = 
            this.performanceStats.totalUpdateTime / this.performanceStats.updateCount;
        this.performanceStats.lastUpdateTime = updateTime;
        this.performanceStats.entityCount = this.entities.size;
        
        this.lastUpdateTime = currentTime;
    }
    
    /**
     * 子类重写的更新方法
     * @param {number} deltaTime - 距离上一帧的时间间隔（秒）
     */
    onUpdate(deltaTime) {
        // 默认实现：遍历所有关注的实体并更新其相关组件
        this.entities.forEach(entity => {
            if (entity.isActive && !entity.isDestroyed) {
                this.updateEntity(entity, deltaTime);
            }
        });
    }
    
    /**
     * 更新单个实体
     * 子类可以重写此方法来实现特定的实体更新逻辑
     * @param {Entity} entity - 要更新的实体
     * @param {number} deltaTime - 距离上一帧的时间间隔（秒）
     */
    updateEntity(entity, deltaTime) {
        // 子类实现
    }
    
    /**
     * 更新系统关注的实体列表
     * @param {Set<Entity>} allEntities - 所有实体的集合
     */
    updateEntityList(allEntities) {
        // 清空当前实体列表
        const previousEntities = new Set(this.entities);
        this.entities.clear();
        
        // 遍历所有实体，找出符合条件的实体
        allEntities.forEach(entity => {
            if (this.matchesEntity(entity)) {
                this.entities.add(entity);
                
                // 如果是新添加的实体，触发事件
                if (!previousEntities.has(entity)) {
                    this.onEntityAdded(entity);
                }
            }
        });
        
        // 检查被移除的实体
        previousEntities.forEach(entity => {
            if (!this.entities.has(entity)) {
                this.onEntityRemoved(entity);
            }
        });
    }
    
    /**
     * 检查实体是否符合系统的处理条件
     * @param {Entity} entity - 要检查的实体
     * @returns {boolean} 是否符合条件
     */
    matchesEntity(entity) {
        if (!entity || entity.isDestroyed || !entity.isActive) {
            return false;
        }
        
        // 检查必需的组件
        const hasRequiredComponents = this.requiredComponents.length === 0 || 
            entity.hasAllComponents(...this.requiredComponents);
        
        if (!hasRequiredComponents) {
            return false;
        }
        
        // 检查排除的组件
        const hasExcludedComponents = this.excludedComponents.length > 0 && 
            entity.hasAnyComponent(...this.excludedComponents);
        
        if (hasExcludedComponents) {
            return false;
        }
        
        // 子类可以重写此方法来添加额外的匹配条件
        return this.onMatchEntity(entity);
    }
    
    /**
     * 子类重写的实体匹配方法
     * @param {Entity} entity - 要检查的实体
     * @returns {boolean} 是否符合条件
     */
    onMatchEntity(entity) {
        // 默认返回true，子类可以重写
        return true;
    }
    
    /**
     * 实体被添加到系统时调用
     * @param {Entity} entity - 被添加的实体
     */
    onEntityAdded(entity) {
        this.emit('entityAdded', { system: this, entity });
        console.log(`实体已添加到系统 ${this.constructor.name}: ${entity.id}`);
    }
    
    /**
     * 实体从系统移除时调用
     * @param {Entity} entity - 被移除的实体
     */
    onEntityRemoved(entity) {
        this.emit('entityRemoved', { system: this, entity });
        console.log(`实体已从系统 ${this.constructor.name} 移除: ${entity.id}`);
    }
    
    /**
     * 手动添加实体到系统
     * @param {Entity} entity - 要添加的实体
     */
    addEntity(entity) {
        if (this.matchesEntity(entity)) {
            if (!this.entities.has(entity)) {
                this.entities.add(entity);
                this.onEntityAdded(entity);
            }
        } else {
            console.warn(`实体 ${entity.id} 不符合系统 ${this.constructor.name} 的条件`);
        }
    }
    
    /**
     * 手动从系统移除实体
     * @param {Entity} entity - 要移除的实体
     */
    removeEntity(entity) {
        if (this.entities.has(entity)) {
            this.entities.delete(entity);
            this.onEntityRemoved(entity);
        }
    }
    
    /**
     * 获取系统中的所有实体
     * @returns {Array<Entity>} 实体数组
     */
    getEntities() {
        return Array.from(this.entities);
    }
    
    /**
     * 获取系统中实体的数量
     * @returns {number} 实体数量
     */
    getEntityCount() {
        return this.entities.size;
    }
    
    /**
     * 设置系统的启用状态
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        if (this.enabled !== enabled) {
            this.enabled = enabled;
            
            this.emit('enabledChanged', { system: this, enabled });
            
            if (enabled) {
                this.onEnabled();
            } else {
                this.onDisabled();
            }
            
            console.log(`系统 ${this.constructor.name} 启用状态已变更: ${enabled}`);
        }
    }
    
    /**
     * 系统启用时调用
     */
    onEnabled() {
        // 子类可以重写此方法
    }
    
    /**
     * 系统禁用时调用
     */
    onDisabled() {
        // 子类可以重写此方法
    }
    
    /**
     * 设置系统的更新频率
     * @param {number} interval - 更新间隔（毫秒），0表示每帧更新
     */
    setUpdateInterval(interval) {
        this.updateInterval = interval;
        console.log(`系统 ${this.constructor.name} 更新间隔已设置: ${interval}ms`);
    }
    
    /**
     * 获取系统的性能统计信息
     * @returns {Object} 性能统计数据
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            systemName: this.constructor.name,
            enabled: this.enabled,
            priority: this.priority,
            updateInterval: this.updateInterval
        };
    }
    
    /**
     * 重置性能统计
     */
    resetPerformanceStats() {
        this.performanceStats = {
            updateCount: 0,
            totalUpdateTime: 0,
            averageUpdateTime: 0,
            lastUpdateTime: 0,
            entityCount: this.entities.size
        };
        
        console.log(`系统 ${this.constructor.name} 性能统计已重置`);
    }
    
    /**
     * 清理系统资源
     */
    dispose() {
        if (this.isDestroyed) {
            return;
        }
        
        console.log(`开始清理系统: ${this.constructor.name}`);
        
        this.isDestroyed = true;
        this.enabled = false;
        
        // 清空实体列表
        this.entities.clear();
        
        // 子类可以重写此方法来执行特定的清理逻辑
        this.onDispose();
        
        // 清理事件监听器
        this.removeAllListeners();
        
        this.emit('disposed', { system: this });
        
        console.log(`系统清理完成: ${this.constructor.name}`);
    }
    
    /**
     * 子类重写的清理方法
     */
    onDispose() {
        // 子类实现
    }
    
    /**
     * 获取系统的调试信息
     * @returns {Object} 调试信息对象
     */
    getDebugInfo() {
        return {
            name: this.constructor.name,
            enabled: this.enabled,
            isInitialized: this.isInitialized,
            isDestroyed: this.isDestroyed,
            priority: this.priority,
            entityCount: this.entities.size,
            requiredComponents: this.requiredComponents,
            excludedComponents: this.excludedComponents,
            updateInterval: this.updateInterval,
            createdAt: this.createdAt,
            performanceStats: this.performanceStats
        };
    }
    
    /**
     * 转换为字符串表示
     * @returns {string} 字符串表示
     */
    toString() {
        return `${this.constructor.name}(entities: ${this.entities.size}, enabled: ${this.enabled})`;
    }
}

export default System;
