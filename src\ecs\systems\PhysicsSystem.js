// src/ecs/systems/PhysicsSystem.js
// 物理系统 - 管理所有物理组件的更新和物理模拟

import { Vector3 } from '@babylonjs/core';
import System from '../System.js';

/**
 * 物理系统类
 * 负责管理所有具有物理组件的实体的物理模拟
 * 处理碰撞检测、力的应用、约束求解等物理计算
 */
export class PhysicsSystem extends System {
    /**
     * 构造函数
     * @param {BABYLON.Scene} scene - Babylon.js场景实例
     * @param {Object} [options={}] - 系统选项
     */
    constructor(scene, options = {}) {
        super(scene, options);
        
        // 设置系统需要的组件
        this.requiredComponents = ['PhysicsComponent', 'TransformComponent'];
        
        // 设置系统优先级（物理系统优先级较高）
        this.priority = 10;
        
        // 物理配置
        this.physicsConfig = {
            gravity: options.gravity || new Vector3(0, -9.81, 0), // 重力
            timeStep: options.timeStep || 1/60,                   // 时间步长
            maxSubSteps: options.maxSubSteps || 3,                // 最大子步数
            enableCCD: options.enableCCD || false,                // 连续碰撞检测
            enableSleeping: options.enableSleeping !== false,     // 启用休眠
            sleepThreshold: options.sleepThreshold || 0.1,        // 休眠阈值
            contactThreshold: options.contactThreshold || 0.02,   // 接触阈值
            enableDebugDraw: options.enableDebugDraw || false     // 启用调试绘制
        };
        
        // 物理引擎引用
        this.physicsEngine = null;
        
        // 物理统计
        this.physicsStats = {
            activeRigidBodies: 0,
            sleepingRigidBodies: 0,
            totalCollisions: 0,
            totalContacts: 0,
            simulationTime: 0,
            lastStepTime: 0,
            averageStepTime: 0,
            stepCount: 0
        };
        
        // 碰撞管理
        this.collisionPairs = new Map(); // entityId1_entityId2 -> collision info
        this.collisionCallbacks = new Map(); // callback id -> callback function
        
        // 力和冲量队列
        this.forceQueue = [];
        this.impulseQueue = [];
        
        // 约束管理
        this.constraints = new Map(); // constraint id -> constraint
        
        // 物理材质管理
        this.physicsMaterials = new Map(); // material name -> material
        
        // 调试绘制
        this.debugDrawEnabled = this.physicsConfig.enableDebugDraw;
        this.debugMeshes = new Map(); // entity id -> debug mesh
        
        console.log('物理系统已创建');
    }
    
    /**
     * 系统初始化
     */
    onInitialize() {
        // 获取物理引擎
        this.physicsEngine = this.scene.getPhysicsEngine();
        
        if (!this.physicsEngine) {
            console.error('物理系统：场景未启用物理引擎');
            return;
        }
        
        // 设置重力
        this.physicsEngine.setGravity(this.physicsConfig.gravity);
        
        // 设置物理引擎参数
        this.setupPhysicsEngine();
        
        // 创建默认物理材质
        this.createDefaultPhysicsMaterials();
        
        // 设置碰撞监听
        this.setupCollisionListeners();
        
        console.log('物理系统初始化完成');
    }
    
    /**
     * 设置物理引擎参数
     * @private
     */
    setupPhysicsEngine() {
        if (!this.physicsEngine) return;
        
        // 设置时间步长
        this.physicsEngine.setTimeStep(this.physicsConfig.timeStep);
        
        // 设置子步数
        if (this.physicsEngine.setSubTimeStep) {
            this.physicsEngine.setSubTimeStep(this.physicsConfig.timeStep / this.physicsConfig.maxSubSteps);
        }
        
        // 启用连续碰撞检测
        if (this.physicsConfig.enableCCD && this.physicsEngine.enableContinuousCollisionDetection) {
            this.physicsEngine.enableContinuousCollisionDetection(true);
        }
    }
    
    /**
     * 创建默认物理材质
     * @private
     */
    createDefaultPhysicsMaterials() {
        // 创建一些常用的物理材质
        const materials = [
            { name: 'default', friction: 0.5, restitution: 0.3 },
            { name: 'ice', friction: 0.1, restitution: 0.1 },
            { name: 'rubber', friction: 0.8, restitution: 0.9 },
            { name: 'metal', friction: 0.3, restitution: 0.2 },
            { name: 'wood', friction: 0.6, restitution: 0.4 }
        ];
        
        materials.forEach(mat => {
            this.createPhysicsMaterial(mat.name, mat.friction, mat.restitution);
        });
    }
    
    /**
     * 创建物理材质
     * @param {string} name - 材质名称
     * @param {number} friction - 摩擦系数
     * @param {number} restitution - 弹性系数
     */
    createPhysicsMaterial(name, friction, restitution) {
        const material = {
            name,
            friction,
            restitution
        };
        
        this.physicsMaterials.set(name, material);
        
        console.log(`物理材质已创建: ${name}`);
    }
    
    /**
     * 设置碰撞监听
     * @private
     */
    setupCollisionListeners() {
        if (!this.physicsEngine) return;
        
        // 监听碰撞事件
        this.physicsEngine.onCollisionObservable?.add((collisionEvent) => {
            this.handleCollisionEvent(collisionEvent);
        });
    }
    
    /**
     * 处理碰撞事件
     * @private
     * @param {Object} collisionEvent - 碰撞事件
     */
    handleCollisionEvent(collisionEvent) {
        const { collidedAgainst, point, distance, impulse, normal } = collisionEvent;
        
        // 查找对应的实体
        const entity1 = this.findEntityByPhysicsBody(collisionEvent.source);
        const entity2 = this.findEntityByPhysicsBody(collidedAgainst);
        
        if (!entity1 || !entity2) return;
        
        // 生成碰撞对ID
        const pairId = this.generateCollisionPairId(entity1.id, entity2.id);
        
        // 记录碰撞信息
        const collisionInfo = {
            entity1,
            entity2,
            point,
            distance,
            impulse,
            normal,
            timestamp: performance.now()
        };
        
        this.collisionPairs.set(pairId, collisionInfo);
        this.physicsStats.totalCollisions++;
        
        // 通知实体的物理组件
        const physicsComponent1 = entity1.getComponent('PhysicsComponent');
        const physicsComponent2 = entity2.getComponent('PhysicsComponent');
        
        if (physicsComponent1) {
            physicsComponent1.handleCollision(collisionEvent);
        }
        
        if (physicsComponent2) {
            physicsComponent2.handleCollision(collisionEvent);
        }
        
        // 触发系统级碰撞事件
        this.emit('collision', {
            system: this,
            entity1,
            entity2,
            collisionInfo
        });
        
        // 执行注册的碰撞回调
        this.executeCollisionCallbacks(collisionInfo);
    }
    
    /**
     * 根据物理体查找实体
     * @private
     * @param {Object} physicsBody - 物理体
     * @returns {Entity|null} 实体
     */
    findEntityByPhysicsBody(physicsBody) {
        for (const entity of this.entities) {
            const physicsComponent = entity.getComponent('PhysicsComponent');
            if (physicsComponent && physicsComponent.physicsAggregate?.body === physicsBody) {
                return entity;
            }
        }
        return null;
    }
    
    /**
     * 生成碰撞对ID
     * @private
     * @param {string} id1 - 实体ID1
     * @param {string} id2 - 实体ID2
     * @returns {string} 碰撞对ID
     */
    generateCollisionPairId(id1, id2) {
        return id1 < id2 ? `${id1}_${id2}` : `${id2}_${id1}`;
    }
    
    /**
     * 执行碰撞回调
     * @private
     * @param {Object} collisionInfo - 碰撞信息
     */
    executeCollisionCallbacks(collisionInfo) {
        this.collisionCallbacks.forEach((callback, id) => {
            try {
                callback(collisionInfo);
            } catch (error) {
                console.error(`碰撞回调执行错误 ${id}:`, error);
            }
        });
    }
    
    /**
     * 实体添加到系统时的处理
     * @param {Entity} entity - 被添加的实体
     */
    onEntityAdded(entity) {
        super.onEntityAdded(entity);
        
        const physicsComponent = entity.getComponent('PhysicsComponent');
        if (physicsComponent && !physicsComponent.isInitialized) {
            // 初始化物理组件
            physicsComponent.initialize();
        }
        
        // 创建调试网格
        if (this.debugDrawEnabled) {
            this.createDebugMesh(entity);
        }
        
        console.log(`实体已添加到物理系统: ${entity.id}`);
    }
    
    /**
     * 实体从系统移除时的处理
     * @param {Entity} entity - 被移除的实体
     */
    onEntityRemoved(entity) {
        super.onEntityRemoved(entity);
        
        // 清理调试网格
        this.removeDebugMesh(entity);
        
        // 清理碰撞记录
        this.cleanupCollisionPairs(entity.id);
        
        console.log(`实体已从物理系统移除: ${entity.id}`);
    }
    
    /**
     * 系统更新
     * @param {number} deltaTime - 时间间隔
     */
    onUpdate(deltaTime) {
        const startTime = performance.now();
        
        // 重置统计
        this.resetPhysicsStats();
        
        // 应用排队的力和冲量
        this.applyQueuedForces();
        this.applyQueuedImpulses();
        
        // 更新物理组件
        this.entities.forEach(entity => {
            this.updateEntity(entity, deltaTime);
        });
        
        // 更新约束
        this.updateConstraints(deltaTime);
        
        // 清理过期的碰撞记录
        this.cleanupExpiredCollisions();
        
        // 更新调试绘制
        if (this.debugDrawEnabled) {
            this.updateDebugDraw();
        }
        
        // 更新统计
        this.updatePhysicsStats(startTime);
    }
    
    /**
     * 更新单个实体
     * @param {Entity} entity - 实体
     * @param {number} deltaTime - 时间间隔
     */
    updateEntity(entity, deltaTime) {
        const physicsComponent = entity.getComponent('PhysicsComponent');
        if (!physicsComponent || !physicsComponent.isPhysicsEnabled) return;
        
        // 更新物理组件
        physicsComponent.update(deltaTime);
        
        // 检查休眠状态
        if (this.physicsConfig.enableSleeping) {
            this.checkSleepState(physicsComponent);
        }
        
        // 更新统计
        if (physicsComponent.physicsAggregate) {
            this.physicsStats.activeRigidBodies++;
        }
    }
    
    /**
     * 检查休眠状态
     * @private
     * @param {PhysicsComponent} physicsComponent - 物理组件
     */
    checkSleepState(physicsComponent) {
        if (!physicsComponent.physicsAggregate) return;
        
        const velocity = physicsComponent.getLinearVelocity();
        const angularVelocity = physicsComponent.getAngularVelocity();
        
        const totalVelocity = velocity.length() + angularVelocity.length();
        
        if (totalVelocity < this.physicsConfig.sleepThreshold) {
            // 可以进入休眠状态
            if (physicsComponent.physicsAggregate.body.setMotionType) {
                // 设置为静态或休眠状态
                this.physicsStats.sleepingRigidBodies++;
            }
        }
    }
    
    /**
     * 应用排队的力
     * @private
     */
    applyQueuedForces() {
        this.forceQueue.forEach(forceData => {
            const { entityId, force, point } = forceData;
            const entity = this.getEntityById(entityId);
            
            if (entity) {
                const physicsComponent = entity.getComponent('PhysicsComponent');
                if (physicsComponent) {
                    physicsComponent.applyForce(force, point);
                }
            }
        });
        
        this.forceQueue.length = 0;
    }
    
    /**
     * 应用排队的冲量
     * @private
     */
    applyQueuedImpulses() {
        this.impulseQueue.forEach(impulseData => {
            const { entityId, impulse, point } = impulseData;
            const entity = this.getEntityById(entityId);
            
            if (entity) {
                const physicsComponent = entity.getComponent('PhysicsComponent');
                if (physicsComponent) {
                    physicsComponent.applyImpulse(impulse, point);
                }
            }
        });
        
        this.impulseQueue.length = 0;
    }
    
    /**
     * 根据ID获取实体
     * @private
     * @param {string} entityId - 实体ID
     * @returns {Entity|null} 实体
     */
    getEntityById(entityId) {
        for (const entity of this.entities) {
            if (entity.id === entityId) {
                return entity;
            }
        }
        return null;
    }
    
    /**
     * 更新约束
     * @private
     * @param {number} deltaTime - 时间间隔
     */
    updateConstraints(deltaTime) {
        this.constraints.forEach((constraint, id) => {
            // 更新约束逻辑
            if (constraint.update) {
                constraint.update(deltaTime);
            }
        });
    }
    
    /**
     * 清理过期的碰撞记录
     * @private
     */
    cleanupExpiredCollisions() {
        const currentTime = performance.now();
        const expireTime = 1000; // 1秒后过期
        
        this.collisionPairs.forEach((collisionInfo, pairId) => {
            if (currentTime - collisionInfo.timestamp > expireTime) {
                this.collisionPairs.delete(pairId);
            }
        });
    }
    
    /**
     * 清理指定实体的碰撞记录
     * @private
     * @param {string} entityId - 实体ID
     */
    cleanupCollisionPairs(entityId) {
        const toDelete = [];
        
        this.collisionPairs.forEach((collisionInfo, pairId) => {
            if (pairId.includes(entityId)) {
                toDelete.push(pairId);
            }
        });
        
        toDelete.forEach(pairId => {
            this.collisionPairs.delete(pairId);
        });
    }
    
    /**
     * 创建调试网格
     * @private
     * @param {Entity} entity - 实体
     */
    createDebugMesh(entity) {
        const physicsComponent = entity.getComponent('PhysicsComponent');
        if (!physicsComponent || !physicsComponent.physicsAggregate) return;
        
        // 这里可以创建调试用的线框网格
        // 显示物理体的形状和边界
        
        console.log(`调试网格已创建: ${entity.id}`);
    }
    
    /**
     * 移除调试网格
     * @private
     * @param {Entity} entity - 实体
     */
    removeDebugMesh(entity) {
        const debugMesh = this.debugMeshes.get(entity.id);
        if (debugMesh) {
            debugMesh.dispose();
            this.debugMeshes.delete(entity.id);
        }
    }
    
    /**
     * 更新调试绘制
     * @private
     */
    updateDebugDraw() {
        if (!this.debugDrawEnabled) return;
        
        this.entities.forEach(entity => {
            const debugMesh = this.debugMeshes.get(entity.id);
            if (debugMesh) {
                // 更新调试网格的位置和旋转
                const transformComponent = entity.getComponent('TransformComponent');
                if (transformComponent) {
                    debugMesh.position.copyFrom(transformComponent.position);
                    debugMesh.rotationQuaternion = transformComponent.rotation.clone();
                }
            }
        });
    }
    
    /**
     * 排队应用力
     * @param {string} entityId - 实体ID
     * @param {Vector3} force - 力向量
     * @param {Vector3} [point] - 作用点
     */
    queueForce(entityId, force, point = null) {
        this.forceQueue.push({ entityId, force, point });
    }
    
    /**
     * 排队应用冲量
     * @param {string} entityId - 实体ID
     * @param {Vector3} impulse - 冲量向量
     * @param {Vector3} [point] - 作用点
     */
    queueImpulse(entityId, impulse, point = null) {
        this.impulseQueue.push({ entityId, impulse, point });
    }
    
    /**
     * 添加碰撞回调
     * @param {string} id - 回调ID
     * @param {Function} callback - 回调函数
     */
    addCollisionCallback(id, callback) {
        this.collisionCallbacks.set(id, callback);
    }
    
    /**
     * 移除碰撞回调
     * @param {string} id - 回调ID
     */
    removeCollisionCallback(id) {
        this.collisionCallbacks.delete(id);
    }
    
    /**
     * 设置重力
     * @param {Vector3} gravity - 重力向量
     */
    setGravity(gravity) {
        this.physicsConfig.gravity = gravity.clone();
        
        if (this.physicsEngine) {
            this.physicsEngine.setGravity(gravity);
        }
        
        this.emit('gravityChanged', { system: this, gravity });
    }
    
    /**
     * 启用/禁用调试绘制
     * @param {boolean} enabled - 是否启用
     */
    setDebugDrawEnabled(enabled) {
        this.debugDrawEnabled = enabled;
        
        if (!enabled) {
            // 清理所有调试网格
            this.debugMeshes.forEach(mesh => mesh.dispose());
            this.debugMeshes.clear();
        } else {
            // 为所有实体创建调试网格
            this.entities.forEach(entity => {
                this.createDebugMesh(entity);
            });
        }
    }
    
    /**
     * 重置物理统计
     * @private
     */
    resetPhysicsStats() {
        this.physicsStats.activeRigidBodies = 0;
        this.physicsStats.sleepingRigidBodies = 0;
        this.physicsStats.totalContacts = 0;
    }
    
    /**
     * 更新物理统计
     * @private
     * @param {number} startTime - 开始时间
     */
    updatePhysicsStats(startTime) {
        const stepTime = performance.now() - startTime;
        
        this.physicsStats.simulationTime = stepTime;
        this.physicsStats.lastStepTime = stepTime;
        this.physicsStats.stepCount++;
        
        // 计算平均步长时间
        this.physicsStats.averageStepTime = 
            (this.physicsStats.averageStepTime * (this.physicsStats.stepCount - 1) + stepTime) / 
            this.physicsStats.stepCount;
    }
    
    /**
     * 获取物理统计
     * @returns {Object} 物理统计信息
     */
    getPhysicsStats() {
        return {
            ...this.physicsStats,
            entityCount: this.entities.size,
            collisionPairCount: this.collisionPairs.size,
            constraintCount: this.constraints.size,
            materialCount: this.physicsMaterials.size
        };
    }
    
    /**
     * 系统清理
     */
    onDispose() {
        // 清理调试网格
        this.debugMeshes.forEach(mesh => mesh.dispose());
        this.debugMeshes.clear();
        
        // 清理约束
        this.constraints.clear();
        
        // 清理碰撞记录
        this.collisionPairs.clear();
        
        // 清理回调
        this.collisionCallbacks.clear();
        
        // 清理队列
        this.forceQueue.length = 0;
        this.impulseQueue.length = 0;
        
        // 清理物理材质
        this.physicsMaterials.clear();
        
        console.log('物理系统已清理');
    }
    
    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            ...super.getDebugInfo(),
            physicsConfig: this.physicsConfig,
            physicsStats: this.getPhysicsStats(),
            hasPhysicsEngine: !!this.physicsEngine,
            debugDrawEnabled: this.debugDrawEnabled
        };
    }
}

export default PhysicsSystem;
