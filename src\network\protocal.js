/**
 * 与服务端消息码对应的客户端配置.
 * 消息事件函数命名规范:
 * 1. 一般性的收到消息的回调事件, 命名为messageReceiveXXXX(payloadIn, metaIn).
 * 2. 回复消息的回调事件, 命名为messageReplyXXXX(payloadIn, metaIn).
 * 3. 主动向服务器发送消息的事件(非回调), 命名为messageSendXXXX(payloadOut, metaOut).
 * 4. 参数要注明是in还是out.
 * @module protocal
 */

import networkManagerInstance from "./networkManager";

// 消息码索引
export const MSG_CODE_HELLO = 0;
export const MSG_CODE_PING  = 1;

const messageReceivePing = (payload, meta) => {
    console.log(`Ping replied by the server with: ${payload}, latency: ${Date.now()-meta.clientTime}ms.`);
}

const messageReceiveHello = (payload, meta) => {
    console.log(`Hello from the server ${payload}, start heart beating!`);
    networkManagerInstance.startHeartbeat();
};

/**
 * 注意: 后面需要考虑在实际逻辑上是否会产生循环依赖！
 * 与服务端消息码对应的客户端配置.
 * 消息对象定义尽可能简单, 将复杂性放到回调函数中.
 * 例如收到消息后调用cb数组中的回调函数, 如果数组为空, 实际上就是没有回调,
 * 如果需要回复, 判断消息是否超时, 都放在回调函数中.
 * @type {Array<Object>}
 * @property {number} code - 消息码, 与服务端定义一致, 用于唯一标识消息类型.
 * @property {string} name - 消息名称, 用于打印日志和调试等.
 * @property {Array<Function>} cb - 当收到此类型的消息时依次执行的其中的回调函数.
 * @property {string} doc - 对此消息协议的描述信息, 方便理解其用途.
 */
const staticProtocals = [
    { code: MSG_CODE_HELLO, name: "HELLO", cb: [messageReceiveHello], doc: "onopen阶段从服务端发起的Hello消息, 用于完成交换密钥等连接上下文初始化的事务."},
    { code: MSG_CODE_PING,  name: "PING",  cb: [messageReceivePing],  doc: "从客户端发起的Ping消息, 用于维持心跳."},

];

/**
 * 操作码到消息对象的映射, 方便快速定位到消息对象.
 * 消息码不会太多, 分步也不会太广, 因此使用数组作为消息码到消息对象的映射.
 * @returns
 */
export const makeCode2Protocal = () => {
    const maxProtocalCode= staticProtocals.reduce((max, protocol) => (protocol.code > max) ? protocol.code : max, 0);
    const operationMap = new Array(maxProtocalCode + 1);
    staticProtocals.forEach(protocol => {
        operationMap[protocol.code] = {...protocol, cb: new Set(protocol.cb)}; // 使用Set, 保证其中内容不会相同
    });

    return operationMap;
};
